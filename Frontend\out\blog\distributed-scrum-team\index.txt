3:I[5613,[],""]
5:I[31778,[],""]
4:["blogDetails","distributed-scrum-team","d"]
0:["m62PJnRiZQT9_nwrRZMuM",[[["",{"children":["blog",{"children":[["blogDetails","distributed-scrum-team","d"],{"children":["__PAGE__?{\"blogDetails\":\"distributed-scrum-team\"}",{}]}]}]},"$undefined","$undefined",true],["",{"children":["blog",{"children":[["blogDetails","distributed-scrum-team","d"],{"children":["__PAGE__",{},["$L1","$L2",null]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children","$4","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/2e2751e26baf52dd.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/51e1a111302b0f86.css","precedence":"next","crossOrigin":""}]]}]]},["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children","blog","children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","notFoundStyles":"$undefined","styles":null}]]},[null,"$L6",null]],[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/0fdd3f077818801d.css","precedence":"next","crossOrigin":""}],["$","link","1",{"rel":"stylesheet","href":"/_next/static/css/2bf4129eb119826a.css","precedence":"next","crossOrigin":""}]],"$L7"]]]]
8:I[85935,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
9:"$Sreact.suspense"
a:I[19721,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],"BailoutToCSR"]
b:I[73214,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
c:I[68365,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
d:I[28667,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","9160","static/chunks/app/not-found-20c6611db7f7de58.js"],""]
e:I[45499,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
f:I[7575,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
10:I[37978,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","8838","static/chunks/8838-35316bd625d3f831.js","8391","static/chunks/8391-64d1caec10606b18.js","7476","static/chunks/7476-a6e367f740f18538.js","3185","static/chunks/app/layout-85f7c750ab62fe25.js"],""]
6:["$","html",null,{"lang":"en","children":[["$","head",null,{"children":[[["$","$L8",null,{"id":"gtm-script","strategy":"lazyOnload","dangerouslySetInnerHTML":{"__html":"\n            (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':\n            new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],\n            j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=\n            'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);\n            })(window,document,'script','dataLayer','GTM-M53BRHCX');\n          "}}],["$","noscript",null,{"children":["$","iframe",null,{"src":"https://www.googletagmanager.com/ns.html?id=GTM-M53BRHCX","height":"0","width":"0","loading":"lazy","style":{"display":"none","visibility":"hidden"}}]}]],["$","link",null,{"rel":"preconnect","href":"https://api.ipify.org"}],["$","link",null,{"rel":"preconnect","href":"https://ipwhois.app"}]]}],["$","body",null,{"className":"__className_9b9fd1","children":[["$","$9",null,{"fallback":null,"children":["$","$La",null,{"reason":"next/dynamic","children":["$","$Lb",null,{}]}]}],["$","$Lc",null,{"headerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-20T04:52:06.170Z","updatedAt":"2025-07-09T08:41:03.762Z","publishedAt":"2024-05-29T09:35:34.042Z","logo":{"id":1,"link":"/","image":{"data":{"id":3564,"attributes":{"name":"Group 5050.svg","alternativeText":"MTL logo","caption":null,"width":277,"height":51,"formats":null,"hash":"Group_5050_ae23f187b6","ext":".svg","mime":"image/svg+xml","size":14.92,"url":"https://cdn.marutitech.com/Group_5050_ae23f187b6.svg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T13:04:45.248Z","updatedAt":"2025-05-06T05:07:56.329Z"}}}},"menu":[{"id":3,"__component":"header.menu-1","title":"Services","link":"/services","button":{"id":2,"title":"All Services","link":"/services"},"subMenu":[{"id":8,"title":"Product Engineering","link":"/services/software-product-engineering","sublinks":[{"title":"Software Product Engineering","link":"/services/software-product-engineering"},{"title":"Quality Engineering","link":"/services/quality-engineering"},{"title":"Maintenance & Support","link":"/services/maintenance-and-support"}]},{"id":9,"title":"DevOps & Cloud Engineering","link":"/services/cloud-application-development","sublinks":[{"title":"Cloud Application Development","link":"/services/cloud-application-development"},{"title":"DevOps","link":"/services/devops-consulting"}]},{"id":10,"title":"Data, Analytics & AI ","link":"/services/artificial-intelligence-consulting","sublinks":[{"title":"Analytics","link":"/services/data-analytics-consulting"},{"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting"}]},{"id":11,"title":"Customer Experience","link":"/services/ui-ux-design-and-development","sublinks":[{"title":"UI/UX Design","link":"/services/ui-ux-design-and-development"},{"title":"Interactive Experience","link":"/services/interactive-experience"}]},{"id":12,"title":"Digital & Technology Consulting","link":"/services/technology-advisory","sublinks":[{"title":"Technology Advisory","link":"/services/technology-advisory"}]},{"id":13,"title":"IT Outsourcing","link":"/services/staff-augmentation","sublinks":[{"title":"Talent Augmentation","link":"/services/staff-augmentation"}]}]},{"id":4,"__component":"header.menu-3","title":"ValueQuest","link":"/cloud-consulting-solution","titleDescription":null,"button":null,"subLinks":[{"id":126,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":131,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":127,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":133,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":4,"__component":"header.menu-2","title":"Industries","link":"/insurance","subLinks":[{"id":92,"title":"Healthcare","link":"/healthcare"},{"id":93,"title":"Legal","link":"/legal"},{"id":94,"title":"Insurance","link":"/insurance"},{"id":118,"title":"Retail","link":"/retail"}]},{"id":3,"__component":"header.menu-3","title":"Resources","link":"/resources","titleDescription":{"id":3,"title":"Latest blog","description":"<p>How to Improve Data Quality with Effective Governance Practices</p>"},"button":{"id":5,"title":"See more","link":"/zero-trust-security-aws-guide/"},"subLinks":[{"id":95,"title":"Blogs","link":"/blog"},{"id":96,"title":"Ebooks","link":"/ebooks"},{"id":97,"title":"Podcasts","link":"/podcasts"},{"id":98,"title":"Case Studies","link":"/case-study"},{"id":99,"title":"Partners","link":"/partners/aws"},{"id":100,"title":"Events","link":"/events"},{"id":101,"title":"Videos","link":"/videos"}]},{"id":1,"__component":"header.menu-4","title":"About Us","link":"/about-us","button":null,"subLinks":[{"id":107,"title":"Our Story","link":"/about-us/#our-story"},{"id":108,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":109,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":110,"title":"In News","link":"/about-us/#in-news"}]}]}},"meta":{}}}],["$","$L3",null,{"parallelRouterKey":"children","segmentPath":["children"],"loading":"$undefined","loadingStyles":"$undefined","loadingScripts":"$undefined","hasLoading":false,"error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":["$","$Ld",null,{}],"notFoundStyles":[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/e899e6280ca68d86.css","precedence":"next","crossOrigin":""}]],"styles":null}],["$","$9",null,{"fallback":null,"children":["$","$Le",null,{"footerData":{"data":{"id":1,"attributes":{"createdAt":"2024-05-15T10:41:22.189Z","updatedAt":"2025-07-08T07:00:07.222Z","publishedAt":"2024-05-29T11:52:51.709Z","sector_row":[{"id":1,"title":"Resources","link":"/resources","Sublinks":[{"id":22,"title":"Blogs","link":"/blog"},{"id":23,"title":"Case Studies","link":"/case-study"},{"id":24,"title":"Videos","link":"/videos"},{"id":25,"title":"Podcast","link":"/podcasts"},{"id":27,"title":"Ebooks","link":"/ebooks"},{"id":28,"title":"Partners","link":"/partners/aws"},{"id":29,"title":"Events","link":"/events/"}]},{"id":2,"title":"Company","link":"/#","Sublinks":[{"id":30,"title":"About Us","link":"/about-us"},{"id":31,"title":"How We Work","link":"/about-us/#how-we-work"},{"id":32,"title":"Leadership Team","link":"/about-us/#leadership-team"},{"id":35,"title":"Contact Us","link":"/contact-us"},{"id":136,"title":"Sitemap","link":"/sitemap"}]},{"id":3,"title":"Careers","link":"/careers","Sublinks":[{"id":39,"title":"Current Opportunities","link":"/careers/#current-opportunities"},{"id":40,"title":"Employee Testimonials","link":"/careers/#employee-testimonials"},{"id":36,"title":"Core Values","link":"/careers/#core-values"},{"id":125,"title":"Benefits","link":"/careers/#benefits"},{"id":37,"title":"Life at MTL","link":"/careers/#life-at-mtl"}]},{"id":4,"title":"Industries","link":"/#","Sublinks":[{"id":41,"title":"Insurance","link":"/insurance"},{"id":42,"title":"Legal","link":"/legal"},{"id":43,"title":"Healthcare","link":"/healthcare"},{"id":119,"title":"Retail","link":"/retail"}]}],"pages_row":[{"id":15,"title":"Cloud Application Development","link":"/services/cloud-application-development","Sublinks":[{"id":86,"title":"Cloud Consulting","link":"/services/cloud-application-development/cloud-computing-consulting"},{"id":87,"title":"Cloud Security Services","link":"/services/cloud-application-development/cloud-security-services"},{"id":88,"title":"Serverless App Development","link":"/services/cloud-application-development/serverless-app-development"},{"id":89,"title":"Microservices Architecture  Development","link":"/services/cloud-application-development/microservices-consulting"},{"id":90,"title":"Cloud Migration Consulting","link":"/services/cloud-application-development/cloud-migration-consulting"},{"id":91,"title":"Cloud Native App Development","link":"/services/cloud-application-development/cloud-native-application-development"}]},{"id":10,"title":"Software Product Engineering","link":"/services/software-product-engineering","Sublinks":[{"id":59,"title":"SaaS Development","link":"/services/software-product-engineering/saas-application-development"},{"id":60,"title":"Web App Development","link":"/services/software-product-engineering/web-application-development"},{"id":61,"title":"Mobile App Development","link":"/services/software-product-engineering/mobile-app-development"},{"id":62,"title":"Low Code No Code Development","link":"/services/software-product-engineering/low-code-no-code-development"}]},{"id":12,"title":"Artificial Intelligence","link":"/services/artificial-intelligence-consulting","Sublinks":[{"id":67,"title":"Computer Vision","link":"/services/artificial-intelligence-consulting/computer-vision"},{"id":68,"title":"Machine Learning","link":"/services/artificial-intelligence-consulting/machine-learning"},{"id":69,"title":"Natural Language Processing","link":"/services/artificial-intelligence-consulting/natural-language-processing"}]},{"id":16,"title":"ValueQuest","link":"/cloud-consulting-solution","Sublinks":[{"id":132,"title":"Data Visualization Services","link":"/data-visualization-services"},{"id":122,"title":"Cloud Cost Optimization","link":"/cloud-consulting-solution"},{"id":124,"title":"MedBrief","link":"/partners/aws/solutions/"},{"id":134,"title":"AI Readiness Audit","link":"/ai-readiness-audit"}]},{"id":13,"title":"Talent Augmentation","link":"/services/staff-augmentation","Sublinks":[{"id":70,"title":"IT Outsourcing","link":"/services/staff-augmentation/it-outsourcing"},{"id":71,"title":"CTO as a Service","link":"/services/staff-augmentation/virtual-cto-services"},{"id":73,"title":"On Demand Agile Teams","link":"/services/staff-augmentation/hire-agile-developers"},{"id":72,"title":"Hire Node.js Developers","link":"/services/staff-augmentation/hire-node-js-developers"},{"id":74,"title":"Hire a Developer - Python","link":"/services/staff-augmentation/hire-python-developers"},{"id":75,"title":"Hire a Developer - Dot Net","link":"/services/staff-augmentation/hire-dot-net-developers"},{"id":76,"title":"Hire a Developer - Angular","link":"/services/staff-augmentation/hire-angular-developers"},{"id":116,"title":"Hire a Developer - React ","link":"/services/staff-augmentation/hire-react-developers"},{"id":77,"title":"Hire Mobile App Developers","link":"/services/staff-augmentation/hire-mobile-app-developers"},{"id":78,"title":"Dedicated Development Team","link":"/services/staff-augmentation/hire-dedicated-development-teams"}]},{"id":9,"title":"Technology Advisory","link":"/services/technology-advisory","Sublinks":[{"id":54,"title":"Code Audit","link":"/services/technology-advisory/code-audit"},{"id":115,"title":"GCP Consulting ","link":"/services/technology-advisory/google-cloud-development"},{"id":58,"title":"Digital Transformation Consulting","link":"/services/technology-advisory/digital-transformation-consulting"},{"id":111,"title":"Enterprise Application Modernization","link":"/services/technology-advisory/enterprise-application-modernization"},{"id":112,"title":"Risk and Compliance ","link":"/services/technology-advisory/risk-and-compliance-services/"},{"id":113,"title":"Product Strategy ","link":"/services/technology-advisory/product-strategy"},{"id":114,"title":"Product Management ","link":"/services/technology-advisory/product-management-consulting"}]},{"id":14,"title":"DevOps","link":"/services/devops-consulting/","Sublinks":[{"id":79,"title":"Integration","link":"/services/devops-consulting/systems-integration"},{"id":80,"title":"CI/CD Services","link":"/services/devops-consulting/ci-cd-solutions"},{"id":81,"title":"Cloud Infrastructure Management","link":"/services/devops-consulting/cloud-infrastructure-services"},{"id":82,"title":"Infrastructure as Code","link":"/services/devops-consulting/infrastructure-as-code"},{"id":83,"title":"Containerization Services","link":"/services/devops-consulting/containerization-services"}]},{"id":11,"title":"Quality Engineering","link":"/services/quality-engineering","Sublinks":[{"id":63,"title":"Security Testing","link":"/services/quality-engineering/security-testing"},{"id":64,"title":"Functional Testing","link":"/services/quality-engineering/functional-testing"},{"id":65,"title":"Automation Testing","link":"/services/quality-engineering/ui-test-automation"},{"id":66,"title":"Performance Testing","link":"/services/quality-engineering/performance-testing"}]},{"id":7,"title":"Data Analytics","link":"/services/data-analytics-consulting","Sublinks":[{"id":50,"title":"Data Engineering","link":"/services/data-analytics-consulting/data-engineering"},{"id":51,"title":"Business Intelligence","link":"/services/data-analytics-consulting/business-intelligence-consulting"}]},{"id":6,"title":"Maintenance & Support","link":"/services/maintenance-and-support","Sublinks":[{"id":48,"title":"Application Support","link":"/services/maintenance-and-support/application-support-services"},{"id":49,"title":"Infrastructure Support","link":"/services/maintenance-and-support/infrastructure-managed-services"}]},{"id":8,"title":"Interactive Experience","link":"/services/interactive-experience","Sublinks":[{"id":52,"title":"Chatbot Development","link":"/services/interactive-experience/chatbot-development"},{"id":53,"title":"Robotic Process Automation","link":"/services/interactive-experience/robotic-process-automation"}]},{"id":5,"title":"UI/UX Design","link":"/services/ui-ux-design-and-development","Sublinks":[{"id":45,"title":"Rapid Prototyping","link":"/services/ui-ux-design-and-development/rapid-prototyping-software"},{"id":47,"title":"User Research & Testing","link":"/services/ui-ux-design-and-development/user-research-and-testing"}]}],"terms_and_condition_section":[{"id":2,"title":"Privacy Policy","link":"/privacy-policy/"},{"id":3,"title":"Cookie Policy","link":"/cookie-policy/"},{"id":4,"title":"Sitemap","link":"/sitemap.xml"}],"company_logo_section":{"id":1,"link":"/","Copyright":"©2025 Maruti TechLabs Pvt Ltd . All rights reserved.","social_platforms":[{"id":1,"link":"https://www.linkedin.com/company/maruti-techlabs-pvt-ltd","image":{"data":{"id":3542,"attributes":{"name":"linkedin.png","alternativeText":"LinkedIn Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_1.png","hash":"thumbnail_1_6f10a7d7cd","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":3.55,"sizeInBytes":3545,"url":"https://cdn.marutitech.com/thumbnail_1_6f10a7d7cd.png"}},"hash":"1_6f10a7d7cd","ext":".png","mime":"image/png","size":1.2,"url":"https://cdn.marutitech.com/1_6f10a7d7cd.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.474Z","updatedAt":"2025-04-22T12:28:13.067Z"}}}},{"id":2,"link":"https://twitter.com/MarutiTech","image":{"data":{"id":3543,"attributes":{"name":"twitter.png","alternativeText":"Twitter Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_2.png","hash":"thumbnail_2_de9436730e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":4.12,"sizeInBytes":4122,"url":"https://cdn.marutitech.com/thumbnail_2_de9436730e.png"}},"hash":"2_de9436730e","ext":".png","mime":"image/png","size":1.47,"url":"https://cdn.marutitech.com/2_de9436730e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T12:27:48.828Z","updatedAt":"2025-04-22T12:29:07.268Z"}}}},{"id":3,"link":"https://www.instagram.com/marutitechlabs/","image":{"data":{"id":3547,"attributes":{"name":"instagram.png","alternativeText":"Instagram Logo","caption":null,"width":240,"height":240,"formats":{"thumbnail":{"name":"thumbnail_Instagram Logo.png","hash":"thumbnail_Instagram_Logo_641af82384","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":37.4,"sizeInBytes":37395,"url":"https://cdn.marutitech.com/thumbnail_Instagram_Logo_641af82384.png"}},"hash":"Instagram_Logo_641af82384","ext":".png","mime":"image/png","size":16.47,"url":"https://cdn.marutitech.com/Instagram_Logo_641af82384.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-23T09:33:33.288Z","updatedAt":"2025-04-23T09:33:48.573Z"}}}},{"id":4,"link":"https://www.youtube.com/channel/UCYUfs2E0HakEmFICFMTKc7A","image":{"data":{"id":3541,"attributes":{"name":"youtube.png","alternativeText":"YouTube Logo","caption":null,"width":341,"height":341,"formats":{"thumbnail":{"name":"thumbnail_YT <EMAIL>","hash":"thumbnail_YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","path":null,"width":156,"height":156,"size":2.39,"sizeInBytes":2388,"url":"https://cdn.marutitech.com/thumbnail_YT_Logo_3x_2bd896146e.png"}},"hash":"YT_Logo_3x_2bd896146e","ext":".png","mime":"image/png","size":1.24,"url":"https://cdn.marutitech.com/YT_Logo_3x_2bd896146e.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2025-04-22T07:26:00.906Z","updatedAt":"2025-04-22T12:31:59.249Z"}}}}],"image":{"data":{"id":14,"attributes":{"name":"maruti_logo.png","alternativeText":null,"caption":null,"width":834,"height":155,"formats":{"small":{"name":"small_maruti_logo.png","hash":"small_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":500,"height":93,"size":17.99,"sizeInBytes":17990,"url":"https://cdn.marutitech.com//small_maruti_logo_5897473ce8.png"},"thumbnail":{"name":"thumbnail_maruti_logo.png","hash":"thumbnail_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":245,"height":46,"size":7.85,"sizeInBytes":7853,"url":"https://cdn.marutitech.com//thumbnail_maruti_logo_5897473ce8.png"},"medium":{"name":"medium_maruti_logo.png","hash":"medium_maruti_logo_5897473ce8","ext":".png","mime":"image/png","path":null,"width":750,"height":139,"size":30.17,"sizeInBytes":30168,"url":"https://cdn.marutitech.com//medium_maruti_logo_5897473ce8.png"}},"hash":"maruti_logo_5897473ce8","ext":".png","mime":"image/png","size":5.46,"url":"https://cdn.marutitech.com//maruti_logo_5897473ce8.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-05-29T11:33:58.640Z","updatedAt":"2024-05-29T11:33:58.640Z"}}}}}},"meta":{}}}]}],["$","div",null,{"id":"scroll_to_top","children":["$","$9",null,{"fallback":null,"children":["$","$Lf",null,{"variant":"scroll_to_top","scroll_to":true}]}]}],["$","$L10",null,{}]]}]]}]
11:T622,{"@context":"https://schema.org","@graph":[{"@type":"Organization","@id":"https://marutitech.com/distributed-scrum-team/#organization","name":"Maruti Techlabs","url":"https://marutitech.com/","sameAs":[]},{"@type":"WebSite","@id":"https://marutitech.com/distributed-scrum-team/#website","url":"https://marutitech.com/","name":"Maruti Techlabs","publisher":{"@id":"https://marutitech.com/distributed-scrum-team/#organization"},"potentialAction":{"@type":"SearchAction","target":"https://marutitech.com/distributed-scrum-team/?s={search_term_string}","query-input":"required name=search_term_string"}},{"@type":"WebPage","@id":"https://marutitech.com/distributed-scrum-team/#webpage","url":"https://marutitech.com/distributed-scrum-team/","inLanguage":"en-US","name":"How To Reinvent the Scrum Process for Modern Distributed Teams","isPartOf":{"@id":"https://marutitech.com/distributed-scrum-team/#website"},"about":{"@id":"https://marutitech.com/distributed-scrum-team/#organization"},"image":{"@type":"ImageObject","@id":"https://marutitech.com/distributed-scrum-team/#primaryimage","url":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg","width":631,"height":417,"caption":"home-hero-image"},"primaryImageOfPage":{"@id":"https://marutitech.com/distributed-scrum-team/#primaryimage"},"datePublished":"2019-03-19T05:53:21+00:00","dateModified":"2020-11-02T08:06:30+00:00","description":"A distributed scrum team, also known as a remote team, is a group of people working as a team on the same project but located at different locations."}]}7:[["$","meta","0",{"name":"viewport","content":"width=device-width, initial-scale=1"}],["$","meta","1",{"charSet":"utf-8"}],["$","title","2",{"children":"How To Reinvent the Scrum Process for Modern Distributed Teams"}],["$","meta","3",{"name":"description","content":"A distributed scrum team, also known as a remote team, is a group of people working as a team on the same project but located at different locations."}],["$","meta","4",{"name":"keywords","content":"AI, ML, software development, custom software solutions, automation, business growth"}],["$","meta","5",{"name":"application/ld+json","content":"$11"}],["$","link","6",{"rel":"canonical","href":"https://marutitech.com/distributed-scrum-team/"}],["$","meta","7",{"name":"google-site-verification","content":"m5paUvh011G69su80n6GgMOOi4w-YuRsbVFAyvf2mYY"}],["$","meta","8",{"property":"og:title","content":"How To Reinvent the Scrum Process for Modern Distributed Teams"}],["$","meta","9",{"property":"og:description","content":"A distributed scrum team, also known as a remote team, is a group of people working as a team on the same project but located at different locations."}],["$","meta","10",{"property":"og:url","content":"https://marutitech.com/distributed-scrum-team/"}],["$","meta","11",{"property":"og:site_name","content":"Maruti Techlabs"}],["$","meta","12",{"property":"og:locale","content":"en-US"}],["$","meta","13",{"property":"og:image","content":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg"}],["$","meta","14",{"property":"og:image:alt","content":"How To Reinvent the Scrum Process for Modern Distributed Teams"}],["$","meta","15",{"property":"og:type","content":"article"}],["$","meta","16",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","17",{"name":"twitter:creator","content":"@MarutiTech"}],["$","meta","18",{"name":"twitter:title","content":"How To Reinvent the Scrum Process for Modern Distributed Teams"}],["$","meta","19",{"name":"twitter:description","content":"A distributed scrum team, also known as a remote team, is a group of people working as a team on the same project but located at different locations."}],["$","meta","20",{"name":"twitter:image","content":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg"}],["$","link","21",{"rel":"icon","href":"https://cdn.marutitech.com/marutitech_logo_fe5e4e6162.svg"}]]
1:null
12:I[74577,["5250","static/chunks/5250-8f64ead11a78c084.js","1607","static/chunks/1607-8faa94da6acac664.js","843","static/chunks/843-540bf1faade281ad.js","8062","static/chunks/8062-63bdb7a8529cc7f5.js","6676","static/chunks/6676-89655d1d15d96ea7.js","4174","static/chunks/app/blog/%5BblogDetails%5D/page-db491dc8e453e004.js"],""]
13:Tb12,<p>Ranjit Atwal, Senior Research Director at Gartner, believes that “A hybrid workforce is the future of work, with both remote and on-site part of the same solution to optimize the employer’s workforce needs.”</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Hey there! This blog is almost about 4100+ words long &amp; not everyone likes to read that much. We understand that.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>This is precisely why we made a podcast on the topic. Mitul Makadia, CEO &amp; Founder of Maruti Techlabs, talks to Bikshita Bhattacharyya about his Agile implementation, strategy, and process during the nascent stages of the company.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>He walks us through the challenges he faced, how the business benefited from scaling Agile, and how important is the scrum master in making Agile a success at the organizational level. Here is a small snippet from that episode. Check it out!</i></span></p><div class="raw-html-embed"><iframe width="1333" height="1000" src="https://www.youtube.com/embed/YAN9PmmjEN4?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="How to balance team efficiency with individual learnings in an agile environment? | Podcast Snippet" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true"></iframe></div><p><a href="https://www.gartner.com/en/newsroom/press-releases/2021-06-22-gartner-forecasts-51-percent-of-global-knowledge-workers-will-be-remote-by-2021" target="_blank" rel="noopener"><u>Gartner forecasts</u></a> that 51% of all knowledge workers worldwide are expected to work remotely by the end of 2021. Remote working varies considerably depending on the IT adoption, culture, and mix of industries globally.</p><p>Scrum is the most reliable methodology for software development that many organizations across the world apply today. However, working with Scrum requires co-located teams for better productivity and communication.&nbsp;</p><p>But at the same time, business needs and unforeseen circumstances (rise of COVID-19) have forced the companies to have them distributed globally. Many studies have proved that the distributed workers are highly productive and have higher satisfaction levels.&nbsp;</p><p>So the question is, how are distributed agile teams and Scrum compatible? Does agile work with the distributed team, and can Scrum Master work remotely? The answer to this question is a distributed scrum team. So, let’s figure it out! We have presented an ultimate guide to understanding what a distributed scrum team is and how it works.</p>14:T933,<p>A distributed scrum team refers to a team where individuals work in the same team on the same project but are located in different locations physically. The distributed scrum team also refers to virtual teams or remote teams, which means that being a distributed team member, you can work from your desired location and collaborate with other team members located at some different location.</p><p>For example, you are the head of the marketing team living in London, collaborating with an SMM specialist located in India and a video manager who lives in California.&nbsp;</p><p><img src="https://cdn.marutitech.com/1e59b2e6-aa-min.png" alt="Distributed Scrum Team" srcset="https://cdn.marutitech.com/1e59b2e6-aa-min.png 1000w, https://cdn.marutitech.com/1e59b2e6-aa-min-768x515.png 768w, https://cdn.marutitech.com/1e59b2e6-aa-min-705x472.png 705w, https://cdn.marutitech.com/1e59b2e6-aa-min-450x302.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>Note that in the above image, the team members located at the exact location physically, i.e., Team A and Team B, are called co-located teams and not distributed teams.</p><p>Generally, remotely distributed teams have constraints on ad hoc collaboration and informal communication. Therefore, they have to be more disciplined about all the agile rules and rituals to find new opportunities to collaborate. Fortunately, most scrum rituals and tools can be adapted to remote environments (including sprints and scrum ceremonies).&nbsp;</p><p>A widespread practice called the “<a href="https://whatis.techtarget.com/definition/two-pizza-rule" target="_blank" rel="noopener"><u>Two Pizza Rule</u></a>” states that two pizzas should feed a team. That means these teams should have up to 7 to 10 members. This rule is recommended for Agile-focused teams; however, it is wise to have smaller teams with 5-6 members for distributed agile methodology.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>What is a Scrum Team?</strong></span></h3><p>It is a group of people, usually five to nine members, working together to supply the required product increments. Scrum is the ultimate weapon for high-level communication among team members so that they can follow the common goal, follow the same rules, and demonstrate respect to each other.&nbsp;</p>15:T1631,<p>Managing the challenges of a virtual team is not easy. Despite all the benefits, there are many challenges faced by agile distributed teams while working remotely. For example:</p><ul><li>Managing different time zones between the distributed team members</li><li>Building strong connections with everyone who is not in the same office&nbsp;</li><li>Accepting each other among different development cultures&nbsp;</li><li>Arranging meets and get-togethers when both teams are online at the same time for a few hours&nbsp;</li></ul><p>The list of challenges is quite long but let us see some common challenges and solutions whenever necessary.&nbsp;</p><p><img src="https://cdn.marutitech.com/ab7769d5-challenges_for_distributed_teams_copy.png" alt="Challenges for Distributed Teams" srcset="https://cdn.marutitech.com/ab7769d5-challenges_for_distributed_teams_copy.png 1000w, https://cdn.marutitech.com/ab7769d5-challenges_for_distributed_teams_copy-768x522.png 768w, https://cdn.marutitech.com/ab7769d5-challenges_for_distributed_teams_copy-705x479.png 705w, https://cdn.marutitech.com/ab7769d5-challenges_for_distributed_teams_copy-450x306.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Different time zone</strong></span></h3><p>Usually, distributed teams have team members located at different time zones. It means scheduling meetings and communicating are pretty tricky for everyone.&nbsp;</p><p><i><strong>Solution</strong></i><strong>:</strong> In this situation, you can contact each other via email. Send your questions to your team members and ask them to answer and solutions.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Lack of communication</strong></span></h3><p>Communicating with your team members across the globe is challenging. This lack of communication is mainly due to different time zones and often causes a delay in work. Without informal hallway chats and impromptu personal meetings, distributed teams need to communicate more at times.&nbsp;</p><p><i><strong>Solution</strong></i><strong>:</strong> You can establish the right communication channel that helps everyone get the correct information at the right time. It is essential to provide the guidelines and working of these channels and ensure that everyone understands it for a smooth workflow. Also, you can adapt the video conferencing calls whenever needed at a particular time zone for bridging the communication gap.&nbsp;</p><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 3. Adopt the company culture</strong></span></h3><p>Adopting the company culture is the biggest hurdle to face by the distributed agile team members. The distributed team members will have different communication styles, contexts, values, and work ethics, which make their culture differ entirely.&nbsp;</p><p><i><strong>Solution</strong></i><strong>:</strong> As the distributed scrum team members usually do not follow the company rules and regulations, identifying their activities and behavior is essential to success with the distributed teams. As face-to-face interaction helps them get lined up with the company culture, you can plan a gathering for all the remote team members once every few months.&nbsp;</p><h3><strong>&nbsp;&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp;4. Lacking in building a solid relationship</strong></span></h3><p>It is vital to build cordial relationships with your team members and boost the same. But as the communication is less frequent among the team members in the distributed team set-up, the relationship becomes even more complex. Therefore, the lack of solid relationships is one of the most critical challenges of a virtual team.&nbsp;</p><p><i><strong>Solution</strong></i><strong>:</strong> The best method to solve this problem is to become more vulnerable, which will help your team members to share their issues with you without any hesitation.&nbsp;</p><p>As communication is the heart of any Scrum Framework, special responsibilities should have been taken to beat these scrum challenges while working in a distributed agile environment.</p><p>Therefore, all the distributed scrum team members should have access to powerful communication tools like video conferencing, webcam, etc., to break down barriers. You can use fantastic software like <a href="https://zoom.us/" target="_blank" rel="noopener"><u>Zoom</u></a>, <a href="https://slack.com/intl/en-in/" target="_blank" rel="noopener"><u>Slack</u></a>, <a href="https://www.atlassian.com/" target="_blank" rel="noopener"><u>Jira</u></a>, <a href="https://www.atlassian.com/software/confluence" target="_blank" rel="noopener"><u>Confluence</u></a>, and <a href="https://trello.com/en" target="_blank" rel="noopener"><u>Trello </u></a>to improve the remote team’s collaboration.</p><p><a href="https://marutitech.com/case-study/revamping-real-estate-platform-using-agile-development/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/b3c3a797_artboard_1_copy_17_2x_68d2726fda.png" alt="Overhauling a High-Performance Property Listing Platform" srcset="https://cdn.marutitech.com/thumbnail_b3c3a797_artboard_1_copy_17_2x_68d2726fda.png 245w,https://cdn.marutitech.com/small_b3c3a797_artboard_1_copy_17_2x_68d2726fda.png 500w,https://cdn.marutitech.com/medium_b3c3a797_artboard_1_copy_17_2x_68d2726fda.png 750w,https://cdn.marutitech.com/large_b3c3a797_artboard_1_copy_17_2x_68d2726fda.png 1000w," sizes="100vw"></a></p>16:T1b1d,<p>Any distributed team for software development should follow the Scrum principle for clear communication, transparency, and dedication towards continuous improvements of the final results. Some of the distributed teams best practices are:&nbsp;</p><ul><li>Focus on results&nbsp;</li><li>Use the right software&nbsp;</li><li>Study and compare other distributed agile industries</li><li>Follow the structure for daily and weekly meetings</li></ul><p>When all these steps are followed, the distributed scrum team can better process the product, and all the operations can run smoothly. Apart from this, the other two essential ways that one has to follow while building and managing the Scrum in software engineering are:</p><ul><li>It would help if you did reality checks of transparency and communication at the proper time.&nbsp;</li><li>Experimenting on different exercises and activities that would be relevant to a distributed agile environment.&nbsp;</li></ul><p>Below are the three essential tips for managing the distributed teams in agile:</p><h3><strong>&nbsp;&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp;1. Fix a flexible structure for work</strong></span></h3><p>It is difficult to find a proper structure among the chaos when your team is working remotely. Flexibility does involve not only the time of work but also the other operations of the product development. Scrum helps the developers write only the meta-information about the product and fill the blank space with the content suitable for the product.&nbsp;</p><p>When we talk about the flexibility of the team members, the following has to be discussed:</p><ul><li><strong>The flow of meetings:</strong> The answer for the questions like when meetings will take place, the meeting agenda, and who needs to be present are found.</li><li><strong>Expectations:</strong> It is needed when working with a large team, and the developers have to mention what they are achieving and what they expect from one another. It is a group activity where the interactions and transparency between the distributed scrum team are improved.</li><li><strong>Which agile tool to be used</strong>: Here, the techniques like writing a DoD, a DoR, team contract, etc., are included.&nbsp;</li></ul><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 2. Build trust among distributed agile team</strong></span></h3><p>Building transparency between the distributed scrum team members is challenging as the team is divided and lacks trust. Hence, building a foundation for trust becomes crucial for effective collaboration. Trust is the critical factor that needs to be built on both sides, i.e., the technical team with the client or the entire business on the client-side.&nbsp;</p><p>Businesses and developers have to collaborate efficiently and effectively to build trust between teams located in different locations. An <a href="https://marutitech.com/services/staff-augmentation/hire-agile-developers/" target="_blank" rel="noopener"><span style="color:#f05443;">agile demand management</span></a> team usually takes responsibility for bringing all the stakeholders on the same page.</p><p><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; 3. Reality check in distributed scrum team:</strong></span></p><ul><li>Does a manager communicate the feedback directly?</li><li>Are sprint retrospectives conducted regularly after every sprint cycle? How does the team react to it?</li><li>Are the impediments taken into consideration by the rest of the team and Scrum Master?</li></ul><p><strong>Tips for building trust among scrum team members:</strong></p><ul><li>Agreeing with the suggestions that are in good intentions for the success of the project. The members should take responsibility for their actions and not blame others if a specific product increment fails.</li><li>Setting up the ground rule that everyone has to follow like:</li><li>How to communicate with each other during urgent issues?</li><li>Setting a time window when no one should be disturbed for non-urgent communications.&nbsp;</li><li>Introducing different forms of communication and understanding that suits the best as a team.&nbsp;</li></ul><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 4. Building self-regulation and self-reliance</strong></span></h3><p>Everyone in the distributed scrum team should take responsibility and ownership of their work as a self-managing team. It requires maturity and taking the leadership of their task and later expanding it to the whole team.</p><p>Self-management, self-reliance, and trust are all interconnected qualities among the team members. They are directly proportional to each other, and therefore, if one of the factors increases, the other two automatically increase.&nbsp;</p><p><strong>What should you look out for?</strong></p><ul><li>Is the goal being delivered at the end of the distributed agile development?</li><li>How is the team accountable for their work and action?</li><li>Do distributed team members share their responsibilities and help each other to fulfill the common goal?</li><li>Can distributed teams describe their current goal and what tasks they are performing to accomplish them?</li><li>Are the members of the team giving feedback to one another?</li><li>Do people trust each other with responsibilities?</li></ul><p><strong>Tips to increase the self-reliance in the team</strong></p><ul><li>Set up the ground rules for the team to collaborate. It becomes easier for everyone to understand their limits and let them know their tasks and expectations.</li><li>Writing down the final goal and the expected task that everyone has to complete. Telling the team members to hold them accountable for their work.</li><li>Find the solution to the problem that arises for the distributed team. Some issues like members attending meetings late often should be handled with care, and the whole team should find a solution together.&nbsp;</li></ul><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Do you wonder what one needs to avoid doing to become a good scrum master? Mitul Makadia does a deep dive on certain limits that every scrum master should take into account while leading his/her team on the path of a successful project. Take a look at the video below👇&nbsp;</i></span></p><div class="raw-html-embed"><iframe width="1333" height="1000" src="https://www.youtube.com/embed/2xfzLUtn0BQ?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="What skills make a good scrum master? | Podcast Snippet" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true"></iframe></div>17:Tc12,<p><img src="https://cdn.marutitech.com/0c3b6518-distributed_team_models.png" alt=" Distributed Team Models" srcset="https://cdn.marutitech.com/0c3b6518-distributed_team_models.png 1000w, https://cdn.marutitech.com/0c3b6518-distributed_team_models-768x644.png 768w, https://cdn.marutitech.com/0c3b6518-distributed_team_models-705x591.png 705w, https://cdn.marutitech.com/0c3b6518-distributed_team_models-450x377.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>There are a total of three different models for distributed teams. Let us discuss them in detail below:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Isolated Scrum</strong></span></h3><p>In this scrum model, the team members are isolated geographically. Therefore, only the on-site team will follow the Scrum model while the distributed agile team may not. The responsibilities and velocity of the distributed team are significantly less compared with the scrum team responsibilities. Hence, the overall momentum of the group becomes less. Therefore, this scrum model is not recommended for distributed agile teams.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Distributed Scrum of Scrum</strong></span></h3><p>This Scrum distributed model in agile includes the work partitioned across the cross-functional, isolated scrum team by eliminating the dependencies between the isolated scrum team. Isolated scrum teams are interlinked by Scrum of Scrum agenda where the Scrum Master from the isolated team meets regularly to exchange the updates of their scrum team.&nbsp;&nbsp;</p><blockquote><p><i>Read Also:&nbsp;</i><a href="https://marutitech.com/guide-to-scrum-of-scrums/" target="_blank" rel="noopener"><i>What is Scrum of Scrums?</i></a></p></blockquote><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Integrated Scrum</strong></span></h3><p>The integrated scrum model has fully distributed teams with members of each team located at multiple locations around the world. Because of the distributed nature of this model, the scrum challenges like communication and execution may arise. Still, you can also resolve them by having daily scrum meetings.&nbsp;</p><p>The integrated scrum model is a recommended model for experienced scrum teams at multiple locations.&nbsp;</p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/0142b808_artboard_1_copy_22_2x_bf4e893687.png" alt="Building a Responsive UX To Facilitate Real-Time Updates &amp; Enhance Customer Service" srcset="https://cdn.marutitech.com/thumbnail_0142b808_artboard_1_copy_22_2x_bf4e893687.png 245w,https://cdn.marutitech.com/small_0142b808_artboard_1_copy_22_2x_bf4e893687.png 500w,https://cdn.marutitech.com/medium_0142b808_artboard_1_copy_22_2x_bf4e893687.png 750w,https://cdn.marutitech.com/large_0142b808_artboard_1_copy_22_2x_bf4e893687.png 1000w," sizes="100vw"></a></p>18:Tbe1,<p>There are three major roles in Scrum – Team, Product Owner, and Scrum Master. In an integrated distributed scrum model, special efforts must be made for all these roles in different locations to work as a single integrated team.</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Team Engagement Model</strong></span></li></ul><p>Even though the distributed team members reside at different locations, they should be managed individually. There shouldn’t be any discrimination between the on-site team members and virtual team members.&nbsp;</p><p>Special efforts should be made to ensure that the on-site and the distributed team members get a chance to work on all the technical and functional areas of the project. It will help to retain the knowledge within the team if the team at one location is down.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Product Owner</strong></span></li></ul><p>It is always wise to have one product owner for the scrum team located at different locations. Some groups have a “proxy” product owner (the one who is representing the role of the product owner in his absence), but at times, a proxy product owner does not have the depth of knowledge as an actual product owner.&nbsp;</p><p>Also, it might create confusion, and sometimes, the decisions made by the proxy product owner need to be reversed after the discussion with the actual product owner. The distributed team can have domain experts who can guide the rest of the team members without a product owner, but overall there should be only one authority who can make the final decisions.&nbsp;</p><p>In distributed teams, there are fewer overlapping hours between teams at different locations. So the product owner needs to make their time available for the question-answer during the overlapping hours.&nbsp;</p><ul><li><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Scrum Master</strong></span></li></ul><p>In the Scrum distributed team model, the scrum master is more challenging and needs to be involved more in the team’s daily activity. As there are more challenges due to the nature of the distributed team, the scrum master has to spare more time to resolve its impacts.&nbsp;</p><p>In the distributed team model, the scrum master is located at any one of the locations and should be available with the team at different locations in overlapping hours. In this situation, the scrum master will help other team members with certain types of issues. But most of the time, when the distributed team is not working, he may not be available to help them with their day-to-day problems.&nbsp;</p><p>It is good to have a Secondary Scrum Master at the virtual location, to avoid this situation. One of the team members from the distributed team can play the role of Secondary Scrum Master. The secondary scrum master cannot replace the professional scrum master, but he will undoubtedly help the team solve their day-to-day issues.&nbsp;</p>19:T28e6,<p><img src="https://cdn.marutitech.com/db82f24e-mmm-min.png" alt="Scrum Retrospective Process for the Modern Distributed Team" srcset="https://cdn.marutitech.com/db82f24e-mmm-min.png 1000w, https://cdn.marutitech.com/db82f24e-mmm-min-768x1367.png 768w, https://cdn.marutitech.com/db82f24e-mmm-min-843x1500.png 843w, https://cdn.marutitech.com/db82f24e-mmm-min-396x705.png 396w, https://cdn.marutitech.com/db82f24e-mmm-min-450x801.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>One of the primary challenges of the distributed team is that the team members are separated by space and time. Scrum Model assumes everyone is co-located and all communication can happen in real-time. Let us discuss some of these scrum rituals and how we can reinvent them.&nbsp;</p><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 1. Get rid of daily meets for regular standup</strong></span></h3><p>The regular standup protocol requires the team members to meet daily at the beginning of the day and figure out what they did yesterday and what they will be doing today.</p><p>In distributed teams, all team members cannot meet at one time as one part of the team may be at the end of the day while the other will be at the beginning or middle of the day. A benefit of a distributed scrum team is that the work can continue even with the timezone difference rather than the limited typical eight hours work. To take advantage of this, you have to create a facility for distributed agile team members to complete their updates at the end of the day to hand over to those beginning their workday.&nbsp;</p><p>In the modern scrum process, the daily standup process is replaced by an asynchronous hand-off ritual at the end of the workday without involving any meetings. The critical task for every member in the distributed team is to report on their work progress and what the next team person needs to do, along with other relevant information.&nbsp;</p><p>Tools like <a href="https://www.notion.so/" target="_blank" rel="noopener"><u>Notion</u></a><u> </u>and <a href="https://slack.com/intl/en-in/" target="_blank" rel="noopener"><u>Slack</u></a>, which involve add-ons like GeekBot, can easily do this task and help you with asynchronous updates. The most significant thing to remember is that you must do the updates in reverse chronological order.&nbsp;</p><p>Apart from this, every distributed team member should assign action times whenever necessary to the other team members. A tool like <a href="https://todoist.com/home" target="_blank" rel="noopener"><u>Todoist</u></a><u> </u>and <a href="https://asana.com/?noredirect" target="_blank" rel="noopener"><u>Asana</u></a><u> </u>works well in this case.&nbsp;</p><p>When everyone is physically located at a different place and works as a distributed scrum team, asynchronously updating everyone’s progress helps the team’s product owner and scrum master change their priorities as required and not wait till the next day’s daily standup.&nbsp;</p><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 2. Deconstruct sprint planning into sequential steps&nbsp;</strong></span></h3><p>The traditional <a href="https://www.atlassian.com/agile/scrum/sprint-planning#:~:text=Sprint%20planning%20is%20an%20event%20in%20scrum%20that%20kicks%20off,that%20work%20will%20be%20achieved.&amp;text=The%20What%20%E2%80%93%20The%20product%20owner,items%20contribute%20to%20that%20goal." target="_blank" rel="noopener"><u>sprint planning</u></a> is a monolithic event where everyone in the team gets together and reviews every story and then plays story point poker to estimate. More often than not, many team members are experts in certain portions of the project, and other members are not yet expert enough to estimate correctly.&nbsp;</p><p>When it comes to the distributed scrum team, you have to think of sprint planning differently. Modern sprint planning can no longer be one event or meeting.&nbsp;</p><ol><li><span style="font-family:Raleway, sans-serif;">The scrum master must create a description of each story that can be studied by the team on their own asynchronously.</span></li><li><span style="font-family:Raleway, sans-serif;">Everyone in the scrum team should not have been estimating and playing story point poker. Only those who are knowledgeable enough about the given story should estimate it.&nbsp;</span></li><li><span style="font-family:Raleway, sans-serif;">The sprint planning task and other estimations can be spread flexibly over a few days, even if completed during the previous sprint.&nbsp;</span></li><li><span style="font-family:Raleway, sans-serif;">The estimates gathered are then published and consumed asynchronously. The estimated owners incorporate updates based on the feedback by the rest of the team.&nbsp;</span></li><li><span style="font-family:Raleway, sans-serif;">Finally, the sprint backlog is created by the scrum master using all the above data.&nbsp;</span></li></ol><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Dodging delays from dependencies by having a backup backlog</strong>&nbsp;&nbsp;</span></h3><p>In a distributed team, dependencies can kill productivity and also cause delays in the final output. Sprint planning needs to plan the dependencies among the stories in the sprint backlog so that the team members can sequence their work in the correct order.&nbsp;</p><p>When you are working on a story, there are times when you get stuck in the middle and need someone to unblock you. The traditional scrum rituals assume you can just walk and talk to anyone. But now, the person you need to speak with to get their performance measurement must be part of how to unblock from your work is remotely located on the other side of the world. It is not a good idea to start with a new story and leave the unfinished one unsolved.&nbsp;</p><p>To avoid the loss of productivity, the team should create a new secondary sprint backlog of tasks that can come into the picture when anyone is stuck. Remember that the secondary backlog never consists of the actual stories. It contains tasks like technical debt, unit testing, bug fixing, documentation, performance audit, etc. Also, the backup backlog must only have tasks that can take anywhere from a few minutes to a maximum of 4 hours.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Making the sprint backlog dynamic&nbsp;</strong></span></h3><p>When working with the traditional scrum process, the sprint backlog doesn’t change during the sprint. But practically, the distributed team is constantly dealing with the changing business circumstances and the challenges among the team. The modern team has to be more flexible in comparison to the traditional scrum team.&nbsp;</p><p>The product owner and the scrum master must continuously refine the product and sprint backlog during the sprint. The team needs to constantly estimate or re-estimate stories to adapt to the changes in the circumstances.&nbsp;</p><p>But in this case, if your sprint backlog is dynamic and can accept the changes right away, it can reduce all the scrum challenges easily and quickly. Client change launch dates, suggested changes in UI or functionality, unexpected technical problems, Covid hits, etc., are easily tackled when scrum master can dynamically manage the sprint backlog.&nbsp;</p><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 5. Improving self-sustainability at the individual level</strong></span></h3><p>The traditional responsibility of the product owner and the scrum master involves adopting the new model. In a conventional scrum team, the scrum master is like a hub. But in a distributed team, the scrum master is not available for a large portion of the working hours for the distributed agile system. For this situation, there are two solutions:</p><p>&nbsp; &nbsp;&nbsp;<strong>a]&nbsp;</strong>Team members must be self-organized and self-sustainable to solve the problems on their own when the scrum master is not available to help with their impediments. However, the result of this depends on the maturity of the team.&nbsp;</p><p>&nbsp; &nbsp;&nbsp;<strong>b]</strong> A scrum master can be identified for a specific time zone in a large enough distributed scrum team. The scrum master must synchronize their work just like any other team member would.&nbsp;</p><p>The distributed team member must learn to depend less on the product owner and scrum master for their day-to-day operations.</p><h3><strong>&nbsp;&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp;6. Evaluating scrum teams&nbsp;</strong></span></h3><p>Traditional scrum models analyze the performance of the scrum teams depending on the velocity and burndown. But with a distributed scrum team, the team’s performance is not just measured on the number of stories and completion of story points.&nbsp;</p><p>How well your team collaborates and communicates with each other is the success factor for distributed agile. As discussed earlier, dynamic sprint backlog and secondary backlog require changes in how velocity is measured and burndown charts are initiated. The performance measurement must be part of how distributed team members manage their productivity, problem-solving ability, and adaptability to changing circumstances.&nbsp;</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Did you find the video snippet on How to balance team efficiency with individual learnings in an agile environment? to be insightful? We have a ~22 min video where Mitul Makadia gets into the weeds, and we discuss&nbsp;about Implementing &amp; Scaling Agile to streamline Software Development. Take a look –</i></span></p><div class="raw-html-embed"><iframe width="1333" height="1000" src="https://www.youtube.com/embed/nDpzfPT1LXw?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="What should the scrum master avoid doing to become a good scrum master? | Podcast Snippet" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true"></iframe></div>1a:Ta6f,<p>The new era of distributed scrum teams requires us to rethink and upgrade the decades-old scrum process. It is necessary to reinvent every step of the traditional scrum process to be asynchronous and hence deconstructed. Each distributed team member must learn to work together across the time zones and manage their work dependencies with other team members who are not easily reachable.&nbsp;</p><p>They must be able to adapt to the changing circumstance every day by being flexible with their work. Enabling the agile team with the right tools can go a long way in making asynchronous communication much easier and effective.&nbsp;</p><p>Even though only a single team member is working remotely, the team should adopt the distributed scrum methodology to share work between different locations, communicate effectively, and achieve the organization’s common goal.&nbsp;</p><p>With the growth of distributed teams and workplaces, it is important to have a clear and concise scrum guide for distributed agile, processes, and different scrum frameworks. You can choose different <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener"><u>scaled agile frameworks</u></a> like Scrum, SAFe or LeSS, whichever works best for your business.</p><p>Apart from the above, the product manager and scrum masters also need to change how they hire and evaluate the team members. In the distributed scrum team, looking for collaboration and communication skills results in a better performing team and satisfying results at the end of the day.</p><p><span style="font-family:Raleway, sans-serif;">We understand how important it is to work in modern methodologies where requirements and technology evolve rapidly. At </span><a href="https://marutitech.com/" target="_blank" rel="noopener"><span style="font-family:Raleway, sans-serif;"><u>Maruti Techlabs</u></span></a><span style="font-family:Raleway, sans-serif;">, we follow Agile, Lean, and DevOps best practices to create a superior prototype through effective collaboration and rapid execution.</span></p><p>Building future-proof software is key to the success of your business. We work with you to develop solutions that provide your company’s customers with a seamless experience by ensuring a customized fit.</p><p>Whether you are a start-up or an enterprise, <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener"><u>get in touch</u></a> with us for a free consultation and see how you can benefit from our <a href="https://marutitech.com/services/software-product-engineering/" target="_blank" rel="noopener"><u>product development services</u></a>.</p>1b:T79e,<p>When you hear the word scrum board, you might get transported back to your childhood days. The image of a whiteboard behind your teacher’s desk and hearing your teacher slobbering about your least favorite subject.&nbsp;&nbsp;</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Hey there! This blog is almost about 4100<strong>+</strong>&nbsp;words&nbsp;long &amp; not everyone likes to read that much. We understand that.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>This is precisely why we made a&nbsp;podcast&nbsp;on the topic. Mitul Makadia, CEO &amp; Founder of Maruti Techlabs, talks to Bikshita Bhattacharyya about his Agile implementation, strategy, and process during the nascent stages of the company.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>He walks us through the challenges he faced, how the business benefited from scaling Agile, and how important is the scrum master in making Agile a success at the organizational level. Here is a small snippet from that episode. Check it out!</i></span></p><p>&nbsp;</p><p>Over the years, scrum tools have become a prerequisite of any Scrum and Agile Software development process. They help you analyze your Scrum and sprints, making your work efficient, effective, and faster. It is a powerful tool that allows you to strategically plan each week by letting you see your current sprint status in real-time! It also enables you to visualize how much work goes into completing something within a set amount of time, which motivates you to further your progress at the end of every sprint.&nbsp;<br>&nbsp;</p><p>If you are new to the Scrum project, understanding how it works might be difficult. But fear not: in this detailed guide, we will walk you through the Scrum board in detail with its different functions, how they work, and why you should choose them. So, let’s get started!</p>1c:T94f,<p>Scrum is a popular framework for breaking down complex problems into smaller tasks. It’s project management software used to visually represent these tasks and Scrum sprints. It’s the center of every sprint meeting to get regular updates and your work split across different workflow stages.&nbsp;</p><p><i>Also Read: </i><a href="https://marutitech.com/guide-to-scrum-sprint-planning/" target="_blank" rel="noopener"><i>A Guide To Scrum Sprint Planning</i></a></p><p>The Scrum board constantly gets updated by the team members and displays all the tasks that should be completed by the end of the Scrum project.&nbsp;</p><p>Like dashboards and timeline views, it’s a project management tool that helps you analyze what’s happening with your Scrum project and team members.</p><p>Scrum board is specifically designed to support Scrum as the <a href="https://assets.kpmg/content/dam/kpmg/be/pdf/2019/11/agile-transformation.pdf" target="_blank" rel="noopener">report</a> suggested that 84% of the company adopting the <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener">Agile methodologies</a>, and 78% use the Scrum framework to implement it<a href="https://assets.kpmg/content/dam/kpmg/be/pdf/2019/11/agile-transformation.pdf">.&nbsp;</a></p><p>Scrum boards can be created both virtually and physically. However, virtual Scrum boards come with numerous benefits, such as it being pretty easy to update and display the task status in real-time.&nbsp;</p><p><strong>In short, the Scrum board can:&nbsp;</strong></p><ul><li>Help to organize the Scrum and sprint backlog along with the individual user stories</li><li>Define the workflow to the Scrum team</li><li>Enable to identify the potential bottlenecks in the project process.&nbsp;</li></ul><p><a href="https://marutitech.com/case-study/revamping-real-estate-platform-using-agile-development/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/understand_scrum_board_03154dda81.png" alt="understand scrum board" srcset="https://cdn.marutitech.com/thumbnail_understand_scrum_board_03154dda81.png 245w,https://cdn.marutitech.com/small_understand_scrum_board_03154dda81.png 500w,https://cdn.marutitech.com/medium_understand_scrum_board_03154dda81.png 750w,https://cdn.marutitech.com/large_understand_scrum_board_03154dda81.png 1000w," sizes="100vw"></a></p>1d:T6b6,<p>It usually consists of a big whiteboard or wall space with multiple columns and sticky notes displaying various phases/status of the tasks and Scrum project.</p><p>&nbsp;1. To Do</p><p>&nbsp;2. Work in Progress</p><p>&nbsp;3. Work in Review</p><p>&nbsp;4. Completed</p><p>In addition, you can also add a column named User Stories to denote the purpose of the rows in the Scrum board table. Inside the Scrum task board, each note represents the task for the sprint or Scrum project. The task which is yet to get started is tagged under the “To Do” category. At the same time, the ”Work in Progress” section consists of the ongoing task of the Scrum project. The tasks tested or reviewed by the team’s experts are under “Work in Review,” whereas the successfully finished work is tagged under the “Done” category.&nbsp;</p><p>If you are new to dealing with the Scrum project, these columns will make you realize how effective your work can become when you follow these strategies. Analyzing your work across the respective status columns can provide instant insights into your current and pending tasks.</p><p>Just like a clean desk drives you with more efficient work, this board will help you visualize your task list properly without clutter and decide what needs to be done next to achieve your final goal.&nbsp;</p><p><img src="https://cdn.marutitech.com/structure_of_scrum_board_d71fd4a7e4.png" alt="structure of scrum board" srcset="https://cdn.marutitech.com/thumbnail_structure_of_scrum_board_d71fd4a7e4.png 245w,https://cdn.marutitech.com/small_structure_of_scrum_board_d71fd4a7e4.png 500w,https://cdn.marutitech.com/medium_structure_of_scrum_board_d71fd4a7e4.png 750w," sizes="100vw"></p>1e:Te71,<p>Scrum teams always face one common issue: deciding whether to go with the online or the physical board. Both have their advantages; however, the online Scrum board is always one step ahead of the physical Scrum task board. Let’s see why:</p><h3><strong>&nbsp; &nbsp; 1. Physical Scrum Board</strong></h3><figure class="image"><img src="https://cdn.marutitech.com/physical_scrum_board_min_1500x825_9a42a6d563.png" alt="Physical Scrum Board"></figure><p>Whether it is a whiteboard or a corkboard, the best advantage of a physical Scrum board is that you can create it on any surface. It can help you hold the daily standups around the board and serve as a constant visual reminder for your sprints or Scrum project.&nbsp;</p><p>If your team works on the same floor, keeping the board in the center of your workspace is convenient to help your teammates stay focused on their tasks and goals. At the same time, the space around the board can serve as the meeting place for quick meets and discussions.&nbsp;</p><p>The physical Scrum board is customizable. As the team continues to work on the Scrum project, they can move the notes inside the Scrum board to their respective columns in the task board.&nbsp;</p><h3><strong>&nbsp; &nbsp; 2. Online Scrum Board</strong></h3><p><img src="https://cdn.marutitech.com/Online_Scrum_Board_2ebcd0dc98.png" alt="Online Scrum Board" srcset="https://cdn.marutitech.com/thumbnail_Online_Scrum_Board_2ebcd0dc98.png 245w,https://cdn.marutitech.com/small_Online_Scrum_Board_2ebcd0dc98.png 500w,https://cdn.marutitech.com/medium_Online_Scrum_Board_2ebcd0dc98.png 750w,https://cdn.marutitech.com/large_Online_Scrum_Board_2ebcd0dc98.png 1000w," sizes="100vw"></p><p>Even though the companies prefer physical Scrum boards for their project management purpose, an online Scrum board is the best alternative to a physical Scrum task board, considering all activities being done by digital platforms these days.&nbsp;&nbsp;</p><p>Instead of sticky notes, the online Scrum board makes use of a digital task card. It is easier to schedule your long-term projects using the online Scrum board as working with the data across the sprints is seamless.&nbsp;</p><p>Online Scrum board is the best choice while working with <a href="https://marutitech.com/distributed-scrum-team/" target="_blank" rel="noopener">Distributed Scrum teams</a>. Whether your teammate is on another floor or another country across the globe, an online Scrum board is much more feasible than a physical Scrum board.&nbsp;</p><p>Compared to the physical Scrum Board, the online Scrum board is entirely customizable. With online Scrum software, you are enabled with various features and filters to view your items on the board and automate your task to move it from one column to another.</p><p><i>Read also :&nbsp;</i><a href="https://marutitech.com/guide-to-scrum-of-scrums/" target="_blank" rel="noopener"><i>Guide to Scrum of Scrums: An Answer to Large-Scale Agile</i></a></p><p><br>&nbsp;The most important advantage of the online Scrum board is that it helps you with real-time updates about the changes. The QA team doesn’t have to update you personally with every minor modification on the board. Also, more than one person can operate the online Scrum board at a time and view it on multiple devices on the go, unlike physical Scrum boards.&nbsp;</p><p>Scrum Master requires the sprint reports to evaluate the progress and performance of workers. Using an online Scrum board, you can generate automatic reports and manage your project dashboard efficiently. These reports can be easily shared and stored using the online Scrum board, which gives a clear edge to the physical ones.&nbsp;</p>1f:T19c3,<p>Kanban board is a project management tool started at Toyota and is quite similar to Scrum boards. The Kanban board divides the workflow of the sprint into different sections such as:</p><ul><li>To do</li><li>Work in progress</li><li>Work under testing&nbsp;</li><li>Complete</li></ul><p>The primary aim of the Kanban board is to manage the volume of work through each section of the project. Your Scrum board will be similar to the Kanban board, depending on how your team works with Scrum methodology.&nbsp;</p><p><img src="https://cdn.marutitech.com/kanban_board_113cdd38b7.png" alt="kanban board" srcset="https://cdn.marutitech.com/thumbnail_kanban_board_113cdd38b7.png 235w,https://cdn.marutitech.com/small_kanban_board_113cdd38b7.png 500w,https://cdn.marutitech.com/medium_kanban_board_113cdd38b7.png 750w,https://cdn.marutitech.com/large_kanban_board_113cdd38b7.png 1000w," sizes="100vw"></p><p>However, the significant difference between the Scrum board and Kanban board is that the Scrum board is frequently used in Agile Software development; in contrast, Kanban boards are often used by every team in organizations.&nbsp;</p><p>Let us discuss some other differences between Kanban Board and Scrum board in detail below:</p><p><img src="https://cdn.marutitech.com/difference_between_scrum_and_kanban_board_d973801a17.png" alt="difference between scrum and kanban board" srcset="https://cdn.marutitech.com/thumbnail_difference_between_scrum_and_kanban_board_d973801a17.png 155w,https://cdn.marutitech.com/small_difference_between_scrum_and_kanban_board_d973801a17.png 498w,https://cdn.marutitech.com/medium_difference_between_scrum_and_kanban_board_d973801a17.png 746w,https://cdn.marutitech.com/large_difference_between_scrum_and_kanban_board_d973801a17.png 995w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Scope of Work</strong></span></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban board:</strong></i> Using the Kanban board, you can trace the workflow of team members working on the project. Further, as required, the team members add and update all the tasks from the “to-do” to the “complete” section.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum board:</strong></i> Simultaneously, the Scrum board traces and manages a single Scrum team’s discrete part of a single sprint.&nbsp;</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Timeline</strong></span><strong>&nbsp;</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban board: </strong></i>It works continuously and usually has a fixed limit to the number of tasks that the team can have. Being customizable, the Kanban board always avoids working as iterations and getting its jobs done by the team members.&nbsp;</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum Board: </strong></i>Scrum boards have a fixed timeline. Each sprint process consists of two weeks, and therefore, the Scrum board lasts for two weeks to finish its task.&nbsp;</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Work in Progress</strong></span></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban Board:</strong></i> The primary aim of the Kanban board is to improve the productivity of the Scrum team. Therefore, the “work in progress” column has a fixed number of tasks.&nbsp;</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum Board:</strong></i> As discussed earlier, the Scrum team has to finish a lot of work under a single sprint cycle. Hence, there are no restrictions to add the number of tasks in the “work in progress” section. Even though there is no limit, you have to finish each task at the end of the sprint.&nbsp;</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Board Content</strong></span><strong>&nbsp;</strong></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban Board: </strong></i>As the Kanban board is used by every organization, which also includes the non-technical teams, it does not consider user stories and sprint backlogs as sections or rows.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum Board:</strong>&nbsp;</i> Scrum team members break down the user stories and add them to the sprint backlog. Later, you can work on these sprint backlogs when the time is right.</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Reports</strong></span></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban Board: </strong></i>It is rarely used for creating reports and graphs for the project. The main objective of the Kanban board is to provide the workflow for the project’s progress to the team.&nbsp;</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum Board: </strong></i>On the other hand, you can use the Scrum data from the Scrum task board to create the reports and velocity charts of the project. Later, these charts measure the progress and number of tasks finished in a sprint cycle.&nbsp;</span></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Ownership</strong></span></h3><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Kanban Board: </strong></i>Every member of the organization uses a kanban board whether he belongs to technical background or not. Hence, it is owned by a department or the whole company.</span></p><p><span style="font-family:Raleway, sans-serif;font-size:16px;"><i><strong>Scrum Board: </strong></i>As a single team handles Scrum projects under any organization, only a few people have ownership of the Scrum board.&nbsp;</span></p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/scrum_board_structure_d2d04c5ea6.png" alt="scrum board structure" srcset="https://cdn.marutitech.com/thumbnail_scrum_board_structure_d2d04c5ea6.png 245w,https://cdn.marutitech.com/small_scrum_board_structure_d2d04c5ea6.png 500w,https://cdn.marutitech.com/medium_scrum_board_structure_d2d04c5ea6.png 750w,https://cdn.marutitech.com/large_scrum_board_structure_d2d04c5ea6.png 1000w," sizes="100vw"></a></p>20:Tf12,<p>The Scrum board is a tool for visualizing the tasks of your Scrum team’s project progression and efforts.&nbsp;</p><p>Are you wondering how a scrum board works? What are the steps to follow? Well, to answer these questions, read the working of a working scrum board below.&nbsp;</p><p><img src="https://cdn.marutitech.com/working_of_a_scrum_board_470f93d519.png" alt="working of a scrum board" srcset="https://cdn.marutitech.com/thumbnail_working_of_a_scrum_board_470f93d519.png 205w,https://cdn.marutitech.com/small_working_of_a_scrum_board_470f93d519.png 500w,https://cdn.marutitech.com/medium_working_of_a_scrum_board_470f93d519.png 750w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Generate a Product Backlog</strong></span></h3><p>The foremost task while beginning with the Scrum software is to create a product backlog. It’s the actual list of tasks you have to deal with to get your Scrum project done.&nbsp;</p><p>Not only do you have to focus on defining the list of tasks, but you also have to decide the priorities in which you should finish those tasks. This product backlog will work as an input to the Scrum sprints along with the user stories.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Assign Roles and Tasks</strong></span></h3><p>Before creating the successful Scrum board, it is necessary to define the individual roles of every member of the team. For instance, the Scrum Master is responsible for conducting the sprint retrospective and reports by coaching other team members to work on the project. The product owner is responsible for maintaining and managing the product backlog. This process will further help you to analyze and categorize the tasks in the Scrum board accordingly.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Choose a Template</strong></span></h3><p>Now it is time for you to adopt a template for your Scrum board. By choosing the correct Scrum board pattern, you can save considerable time and answer all the questions you face while project sprints. Moreover, a template helps you maintain the consistency of your Scrum and sprint so that every team member has the same layout to work.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Build your Task Board as a Team</strong></span></h3><p>At this stage, you have to create your Scrum board by adding tasks, user stories, features, and requirements after discussing them with your Scrum team. Divide the tasks, flesh out individual product backlog items, and estimate how long each task will take.&nbsp;<br><br>All team members efficiently allocating the resources should be willing to collaborate to flesh out user stories into tangible work items and assign different workflow steps to team members with relevant skills.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Scrum Meetings&nbsp;</strong></span></h3><p>Building a successful Scrum board is not the final goal; coordinating with the members is essential. That’s why the idea of <a href="https://clickup.com/blog/scrum-meetings/" target="_blank" rel="noopener">Scrum meetings</a> is the best choice to communicate with the Scrum team and alert them with the progress of the project.</p><figure class="image"><img src="https://cdn.marutitech.com/daily_standup_scrum_min_9d55bd3465.png" alt="daily-standup-scrum"></figure><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. Sprint Review</strong></span></h3><p>As soon as we end with the sprint processes, the Scrum Master conducts the sprint review to analyze the project and performance of the Scrum team. The necessary feedback is returned for the modifications, and later the final project is deployed.&nbsp;</p>21:Tb95,<p>So, why use a Scrum board anyway? What is the objective of the Scrum board? Here's what a Scrum board offers to the <a href="https://marutitech.com/services/staff-augmentation/hire-agile-developers/" target="_blank" rel="noopener"><span style="color:#f05443;">agile development team</span></a>.&nbsp;</p><p><img src="https://cdn.marutitech.com/benefits_of_a_scrum_board_d7b404c626.png" alt="benefits of a scrum board" srcset="https://cdn.marutitech.com/thumbnail_benefits_of_a_scrum_board_d7b404c626.png 205w,https://cdn.marutitech.com/small_benefits_of_a_scrum_board_d7b404c626.png 500w,https://cdn.marutitech.com/medium_benefits_of_a_scrum_board_d7b404c626.png 750w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Increase transparency</strong></span></h3><p>Transparency allows the team to share the responsibilities and the tasks of the entire project on which you are working. You have visibility of all the processes of your Scrum project and can keep track of the progress. Team members cannot hide the information on the Scrum task board, which will further provide you with proactive problem-solving approaches.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Increase team communication and efficiency</strong></span><strong>&nbsp;</strong></h3><p>The primary purpose of the Scrum board is to bring the Scrum team together. It helps display your team’s progress and investigate the conversations around different project columns, specifically when someone’s deliverables can affect the entire project. You can find the number of tasks remaining to work on along with the lists of finished tasks to help your team encourage their accomplishments.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Easy to setup and deployment</strong></span><strong>&nbsp;</strong></h3><p>An online scrum board is very straightforward to install and use. User-friendly interface and default Scrum tools make it easy and fast to implement the Agile methodologies. You can smoothly drag and drop the tasks among the sections during the sprint cycle. You can also generate charts, graphs, and reports using the powerful automation feature, which helps the new user to get used to the Scrum methodology.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Make it easy to recognize problem areas</strong></span></h3><p>While working with the Scrum project full of different tasks divided between the Scrum teams, you can’t forget any of those while working with the virtual Scrum board. You can quickly identify your priorities and reduce the issues of continuous communication between the team to analyze the risks and their precautions. Anyone from your team can indicate the problem affecting the project, and other members can resolve them with their expertise to encourage the team to pick up the pace.&nbsp;</p>22:T124c,<p>Just knowing the benefit of the Scrum board will not help you to use it effectively. Here’s some tip to get the most out of it while working with the Scrum software:</p><p><img src="https://cdn.marutitech.com/Tips_on_Creating_an_Effective_Scrum_Board_4fa3fe23d0.png" alt="Tips on Creating an Effective Scrum Board&nbsp;" srcset="https://cdn.marutitech.com/thumbnail_Tips_on_Creating_an_Effective_Scrum_Board_4fa3fe23d0.png 205w,https://cdn.marutitech.com/small_Tips_on_Creating_an_Effective_Scrum_Board_4fa3fe23d0.png 500w,https://cdn.marutitech.com/medium_Tips_on_Creating_an_Effective_Scrum_Board_4fa3fe23d0.png 750w," sizes="100vw"></p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. Create detailed tasks</strong></span></h3><p>While working in the Scrum software, a task usually refers to a small job done by a single team member within a day or less. Therefore, tasks help you break down the user stories, and hence it’s crucial to define them clearly and in detail. It is irrelevant to include the tasks which are too long (4 days+) or too short (1-2 hours).&nbsp;</p><p>To identify the final goal over the Scrum board, you must discuss and set all the parameters of the multiple tasks during the Scrum meetings. You must provide sufficient details about the various tasks of your project without disturbing your team with unnecessary processes.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. Properly assign resources</strong></span></h3><p>As the Scrum Master is the facilitator of the Scrum framework and all other Scrum processes,&nbsp; show their importance while allocating the resources efficiently. It is up to the Scrum Master to help the team optimize their transparency, delivery flow, and schedule the resources.&nbsp;</p><p>It is wise to import the user stories which are pretty relevant from your main product backlog. Avoid adding undefined requirements and divert the focus from the sprint goal. When these resources are appropriately assigned, the sprint will be more effective and efficient.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. Hold effective scrum ceremonies</strong></span></h3><p>It is not a surprise that clear communication is a prominent factor for a successful undertaking. Daily Scrum is the primary platform of communication when you are involved with the Scrum sprints. These Scrum meetings should be short and have a clear goal with tight deadlines to ensure that the team’s progress is reflected on your Scrum board.&nbsp;</p><p>The objective of these meetings is to answer questions like:</p><ul><li>What did we do yesterday?</li><li>What will we be doing today?</li><li>Is there anything that stops us from reaching the final goal?</li></ul><p>Also, the average length of a regular sprint is <a href="https://resources.scrumalliance.org/Collection/scrum-alliance-ebooks" target="_blank" rel="noopener">2.4 weeks</a>, whereas the scrum projects tend to last for an average of 11.6 weeks. Therefore, it is wise not to cover everything in your first sprint.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>4. Don’t include too much information on cards</strong></span></h3><p>The last thing you wish to do with your Scrum board is overload each section with tasks. It distracts the team from its purpose and the final output. A suggestion is to add the tasks in the column where there is the capacity to complete them. You can also link the information to another source or attach doc for a clear representation of your Scrum board and avoid mess.&nbsp;</p><p>To analyze whether the team has enough work to do, it is recommended to identify the balance between feast and famine. If you find any barriers, you might stop and clear them up before moving on with the workflow. You can also use separate boards for parts of the project to focus on what’s important and not clutter with the work unrelated to your sprint.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. Keep everything visible</strong></span></h3><p>The purpose of the Scrum board is not just to provide the chart to balance the project workflow but also to provide transparency and visibility to all team members. It helps every team member identify who is working on what, for how long, whether anyone is facing any issues with their work, etc.</p><p>It includes the key stakeholders who have an absolute interest in the progress of your project. Ensure that the Scrum board is a single trusted source of information for your sprint by including everything relevant to the Scrum software.&nbsp;</p>23:T1c10,<p><img src="https://cdn.marutitech.com/using_the_right_tool_for_the_job_f66d9d3b7f.png" alt="using the right tool for the job" srcset="https://cdn.marutitech.com/thumbnail_using_the_right_tool_for_the_job_f66d9d3b7f.png 179w,https://cdn.marutitech.com/small_using_the_right_tool_for_the_job_f66d9d3b7f.png 500w,https://cdn.marutitech.com/medium_using_the_right_tool_for_the_job_f66d9d3b7f.png 750w," sizes="100vw"></p><p>There are hundreds of Scrum software available in the market with a fantastic set of features and different pricing.&nbsp;</p><p>Below are some commonly used online Scrum software used in 2021:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>1. </strong></span><a href="https://www.zoho.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Zoho Sprints&nbsp;</strong></span></a></h3><p>Using Zoho Sprints, you can automatically generate the reports and provide unique features which complement all stages of your Scrum process.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>2. </strong></span><a href="https://www.pivotaltracker.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Pivotal Tracker</strong></span></a></h3><p>Inspired by the agile software methods, the pivotal tracker is the story-based project planning tool to get regular updates and incremental tweaks during Scrum and sprint cycles.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>3. </strong></span><a href="https://www.atlassian.com/software/jira" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>JIRA</strong></span></a></h3><p>Jira is one of the most popular Scrum software, including features like Issue management, code repository, and release management. According to the <a href="https://www.atlassian.com/customers" target="_blank" rel="noopener">reports by Atlassian</a>, 83% of Fortune 500 companies make use of the Jira scrum board for their project management requirements.</p><h3><span style="color:hsl(0,0%,0%);font-family:Poppins, sans-serif;font-size:18px;"><strong>4.</strong></span><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong> </strong></span><a href="https://asana.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Asana</strong></span></a><a href="https://www.googleadservices.com/pagead/aclk?sa=L&amp;ai=DChcSEwiJ7qmA9PPzAhXck2YCHY64AI8YABAAGgJzbQ&amp;ae=2&amp;ohost=www.google.com&amp;cid=CAESQeD2dcwDx8CpS7VjFJBEHewR2uI0KVmM9ih2SJ3fzmG8giFTIW_VX0S9i0ITCA7YvClNAlVidMOHDr9fo0uH3wz5&amp;sig=AOD64_3uD9jNFZaby3zHs9UFg82mJXhLpw&amp;q&amp;adurl&amp;ved=2ahUKEwjQjKCA9PPzAhXdxzgGHcJZAtQQ0Qx6BAgCEAE" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Asana</strong></span></a></h3><p>Asana is the best Scrum management tool that helps you and your team track your project progress and organize the resources. It is also very helpful in communication between team members and tracking deadlines.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>5. </strong></span><a href="https://trello.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>Trello</strong></span></a></h3><p>Trello is the ultimate Scrum software that helps you to organize your projects into boards. Trello allows you to identify who’s working on what, when, where, and what is still left to work on.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>6. </strong></span><a href="https://monday.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>monday.com</strong></span></a></h3><p>monday.com is one of the great Scrum tools to manage your team and projects. It helps track your project progress and capabilities with customizable notifications and automatically leads you to what’s essential.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>7. </strong></span><a href="https://clickup.com/" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>ClickUp</strong></span></a><a href="https://www.googleadservices.com/pagead/aclk?sa=L&amp;ai=DChcSEwj9qq-d9PPzAhUJeSoKHY20BUIYABAAGgJ0bQ&amp;ae=2&amp;ohost=www.google.com&amp;cid=CAESQeD22QDu4trHD_xVypFslNid2qG4GhX9s2aM0SyhVf8_eg_penJuWf6T9FWw78mowUag6SoyoXg4V56GTBMg6rOZ&amp;sig=AOD64_38hnSdbKvDaDQU2jDDMxT044ikJg&amp;q&amp;adurl&amp;ved=2ahUKEwj7gKSd9PPzAhXxxzgGHfylBkIQ0Qx6BAgCEAE" target="_blank" rel="noopener"><span style="color:#f05443;font-family:Poppins, sans-serif;font-size:18px;"><strong>ClickUp</strong></span></a></h3><p>Apart from Scrum project management, ClickUp enables you with time tracking, training resources, workflow management, etc. It is customizable and provides expandable functionalities, and helps to focus on what’s easy to learn.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>Scrum Board Tools Used By MarutiTechlabs</strong>&nbsp;</span></h3><p>At Marutitechlabs, a big part of our team is dedicated to project management. We use<i> tools such as Jira, Trello, and Asana </i>for the same. These tools help us track each project and its progress. For some projects, teams use Jira to track the progress made. They can easily assign tasks to team members on multiple projects. We also use Asana to keep track of the noted tasks but not given to anyone on the team. Trello is our shared resource for writing down tasks, organizing them by priority, and assigning them to team members. We also use it to create a Kanban-style board for tracking the progress of a project.</p><p>Using these tools allows our team to keep an organized structure and make sure everyone is on the same page.<br>&nbsp;</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Did you find the video snippet on&nbsp;What were some improvements &amp; iterations made while implementing agile in product development?&nbsp;to be insightful? We have a ~22 min video where Mitul Makadia gets into the weeds, and we discuss about Implementing &amp; Scaling Agile to streamline Software Development. Take a look –</i></span></p><div class="raw-html-embed"><iframe width="1500" height="844" src="https://www.youtube.com/embed/t9PeY145obc?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="The Lab Cast Ep 03: Implementing and Scaling Agile to streamline Software Development | Podcast" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true" data-lf-yt-playback-inspected-lynor8xobloawqjz="true" data-gtm-yt-inspected-8="true" id="594748286"></iframe></div>24:T7af,<p>The Scrum board is one of the fundamental tools used in Scrum. Using a Scrum board will help your team members and your organization become more efficient and level up the quality of your product. Moreover, the scrum boards allow you to analyze project performance, anticipate risks and solutions, optimize workflow, and much more.&nbsp;</p><p>Think of it like this: A scrum board is like Thor’s hammer. It is even powerful and invaluable when coupled with the correct project management practices.</p><p>Generally, Scrum boards are often confused with the Kanban boards; however, Scrum boards provide better visual and interactive features to work with your current sprint cycle. The Scrum board is a tool for viewing progress and estimating remaining effort. Therefore, it is not only used for managing the project workflow but also to visualize the outcomes of your team for the current Scrum project. Hence, an online Scrum board is the best addition to any Scrum team.&nbsp;</p><p>At <a href="https://marutitech.com/" target="_blank" rel="noopener">Maruti Techlabs</a>, we thrive to provide high-quality services with a wide range of technologies. With the help of our <a href="https://marutitech.com/services/software-product-engineering/" target="_blank" rel="noopener">Product Development Services</a>, our experts enable you with fast and frequent revisions to your development cycles using Agile, Lean, and DevOps best practices and increase the speed at which projects are delivered.&nbsp;</p><p>Right from building something new, improving what you have, or helping you discover what you need – we do it all. Whether you are a startup or a business enterprise, we work towards helping you build and scale future-proof and intuitive digital products while guiding you with the best processes &amp; practices.</p><p>Curious to learn more? <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener">Get in touch</a> with us today!</p>25:Tcfd,<p>The number of people on your team can significantly impact your business. When you’re starting your business initially, it’s easy to keep track of everything that needs to be done. But as the team grows, things can get out of hand.&nbsp;</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Hey there! This blog is almost about 2600<strong>+</strong>&nbsp;words&nbsp;long &amp; not everyone likes to read that much.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>We understand that.This is precisely why we made a&nbsp;podcast&nbsp;on the topic. Mitul Makadia, CEO &amp; Founder of Maruti Techlabs, talks to Bikshita Bhattacharyya about his Agile implementation, strategy, and process during the nascent stages of the company.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>He walks us through the challenges he faced, how the business benefited from scaling Agile, and how important is the scrum master in making Agile a success at the organizational level. Here is a small snippet from that episode. Check it out!</i></span><br>&nbsp;</p><div class="raw-html-embed"><iframe width="1333" height="1000" src="https://www.youtube.com/embed/PeLcdBWSG3Q?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="What are the benefits of smaller pizza-sized teams? | Podcast Snippet" data-lf-yt-playback-inspected-lynor8xobloawqjz="true" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true" data-gtm-yt-inspected-8="true" id="*********"></iframe></div><p>At the same time, if multiple teams work parallel on one product, they need to communicate regularly and effectively. In most cases, people across many teams will not collaborate closely or even see each other regularly, so how can they communicate efficiently? How can they divide work between them if they don’t meet each other face-to-face?</p><p>For decades, the Scrum Guide has proven to be a helpful resource in supporting teams and companies that need to address these issues. Scrum is a framework for developing products, which embraces empiricism and is optimized for complex projects.&nbsp;</p><p>Here’s when the Scrum of Scrums technique comes to play. Scrum of Scrums is the process of managing multiple Scrum-based projects of any size as an integrated and unified business process. As Scrum is one of the most popular <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener">agile frameworks</a>, it requires a unique set of capabilities and a shift in thinking for everyone involved.&nbsp;</p><p>Scrum of Scrums refers to a customer and <a href="https://marutitech.com/guide-to-project-management/" target="_blank" rel="noopener">project management </a>technique that utilizes concurrent development rather than serial. It provides a lightweight way to manage the interactions between several scrum teams across the organization.&nbsp;</p><p>This guide will study the working, structure, and benefits of a scrum of scrum practices in detail to help you scale and integrate your work with multiple Scrum teams working on the same project.&nbsp;</p>26:T430,<p>The Scrum of Scrums framework was first introduced by Jeff Sutherland and Ken Schwaber in 1996 while operating at the Lawrence Livermore National Laboratory. The original purpose of this framework was to coordinate the activities of eight business units with multiple product lines per business unit in a single development cycle.&nbsp;</p><p>Sutherland and Schwaber found that having separate scrum teams for each business unit impeded workflow within and across business units, so they experimented. They gathered all eight product teams into a single room. They had their work together by forming a meta-team or “Scrum of Scrums” to create an environment where independent teams could synchronize their efforts more efficiently.</p><p>Later, in 2001, Sutherland published this experience under the title “<a href="https://jeffsutherland.com/papers/scrum/Sutherland2001AgileCanScaleCutter.pdf" target="_blank" rel="noopener">Agile Can Scale: Inventing and Reinventing SCRUM in Five Companies</a>,” which mentioned Scrum of scrums for the first time.&nbsp;</p>27:T884,<p>Scrum of Scrums is designed to be a lightweight solution for scaling agile methods. The main benefit of the Scrum of Scrums approach is to provide a way to enhance communication by connecting people from different Scrum teams who need to collaborate and coordinate with each other.&nbsp;</p><p>The essential Scrum of Scrums’ purpose is that multiple teams are working on a single product, and there needs to be a way for all of these teams to communicate with each other.&nbsp;</p><p>It’s particularly relevant for organizations with teams across geographies and time zones because it provides a means for teams to synchronize their work, communicate any issues or delays, and coordinate planning activities.&nbsp;</p><p>According to the definition of <a href="https://en.wikipedia.org/wiki/Jeff_Sutherland" target="_blank" rel="noopener">Jeff Sutherland</a>, “Scrum of scrums as I have used it is responsible for delivering the working software of all teams to the Definition of Done at the end of the Sprint, or for releases during the sprint.”</p><p>A Scrum of Scrums (SoS) is a meeting between two sprints, where the development team discusses their inter-team dependencies. The scaled agile framework is run by the development team members, who are best positioned to discuss inter-team dependencies and find a solution.</p><p>Scrum of Scrums helps deploy and deliver complex products by adapting transparency and inspection at a large scale. It enables scrum teams to work towards common goals and complete the project by aligning.&nbsp;</p><p>Participants present at the Scrum of Scrums answer similar questions like daily Scrum. For instance:</p><ul><li>What has been the team’s progress since we last met?</li><li>What problems are the team facing, and can the other teams resolve them?</li><li>What tasks will the team carry out before the next meet?</li></ul><p>There are various techniques by which you can implement the Scrum of Scrums. It could be a meeting within the team or with all teams. Therefore, scrum of scrum definition aims to get all teams in sync with each other so that any dependencies between teams have been identified and resolved.</p>28:T8d0,<p>A Scrum of Scrums meeting can be a valuable way to communicate with organizations with different goals. Here’s how:</p><ul><li><span style="font-family:Raleway, sans-serif;">Organizations use this approach as the initial step to scale agile and organize the delivery of large, complex products.</span></li><li><span style="font-family:Raleway, sans-serif;">The Scrum of Scrums supports the agile teams by enhancing their productivity and coordinating their work with other teams.</span></li><li><span style="font-family:Raleway, sans-serif;">When problems arise in one part of a system, they can affect the rest of the system directly and indirectly. Scrum of Scrums provides an effective way to identify these issues and address them on time.</span></li><li><span style="font-family:Raleway, sans-serif;">Through this meeting, representatives from each team can share updates about their progress and report on issues that may have arisen.</span></li><li><span style="font-family:Raleway, sans-serif;">Scrum of Scrum meetings helps ensure that tasks are synchronized, and team members are kept up to date with the work remaining on their project.</span></li><li><span style="font-family:Raleway, sans-serif;">Scrum-of-Scrum teams not only coordinate delivery but ensure a fully integrated product at the end of every sprint.</span></li><li><span style="font-family:Raleway, sans-serif;">Scrum meetings are also helpful for solving problems and making decisions.&nbsp;</span></li><li><span style="font-family:Raleway, sans-serif;">This meeting helps ensure transparency by providing everyone with the latest information on the project.</span></li></ul><p><a href="https://marutitech.com/case-study/revamping-real-estate-platform-using-agile-development/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/guide_to_scrums_of_scrums_5379b631da.png" alt="guide to scrums of scrums" srcset="https://cdn.marutitech.com/thumbnail_guide_to_scrums_of_scrums_5379b631da.png 245w,https://cdn.marutitech.com/small_guide_to_scrums_of_scrums_5379b631da.png 500w,https://cdn.marutitech.com/medium_guide_to_scrums_of_scrums_5379b631da.png 750w,https://cdn.marutitech.com/large_guide_to_scrums_of_scrums_5379b631da.png 1000w," sizes="100vw"></a></p>29:T68d,<p><img src="https://cdn.marutitech.com/structure_of_scrum_of_scrums_11bd0d3feb.png" alt="structure of scrum of scrums" srcset="https://cdn.marutitech.com/thumbnail_structure_of_scrum_of_scrums_11bd0d3feb.png 236w,https://cdn.marutitech.com/small_structure_of_scrum_of_scrums_11bd0d3feb.png 500w,https://cdn.marutitech.com/medium_structure_of_scrum_of_scrums_11bd0d3feb.png 750w," sizes="100vw"></p><p>A Scrum of Scrums team is a cross-functional team that includes representatives from multiple Scrum teams. It follows the same practices and events as an individual Scrum team, and each member of the Scrum of Scrums team has the same role as a member of the corresponding Scrum team. However, to deploy the potentially integrated product at the end of every sprint, new additional roles are included here, not found in Scrum teams.&nbsp;</p><p>For instance, there is the quality assurance leader in every Scrum of Scrums team. The quality assurance leader is responsible for overseeing, testing, and maintaining the quality of the final product at the end of each sprint.&nbsp;</p><p>Another such role is Scrum of Scrums Master, who is responsible for focusing on the progress and product backlogs, facilitating prioritization, and continuously improving the effectiveness of Scrum of Scrums.&nbsp;</p><p>These roles take up the 15 minutes of scaled daily Scrum meet-ups to align and improve the impediments of the project. Here, each team’s product owner or ambassador discusses each team’s requirements, risks, and sprint goals with the other team. It also identifies the improvements of their team that other groups can leverage to achieve the final product.&nbsp;</p>2a:T9fc,<p><img src="https://cdn.marutitech.com/961868d3-benefits-of-a-scrum-of-scrums.png" alt="Benefits-of-a-Scrum-of-Scrums" srcset="https://cdn.marutitech.com/961868d3-benefits-of-a-scrum-of-scrums.png 1000w, https://cdn.marutitech.com/961868d3-benefits-of-a-scrum-of-scrums-768x1095.png 768w, https://cdn.marutitech.com/961868d3-benefits-of-a-scrum-of-scrums-494x705.png 494w, https://cdn.marutitech.com/961868d3-benefits-of-a-scrum-of-scrums-450x642.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>Scrum of Scrums is indeed considered one of the <a href="https://marutitech.com/services/staff-augmentation/hire-agile-developers/" target="_blank" rel="noopener"><span style="color:#f05443;">agile best practices for more effective teams</span></a>. It facilitates better collaboration, coordination, scalability, and flexibility, especially in larger and more complex projects. Here are some key points highlighting the benefits and principles of Scrum of Scrums:</p><ul><li>Scrum of Scrums enables you to streamline the cross-team collaboration between different teams working on the same project.&nbsp;</li><li>SoS is more accessible for large enterprises to handle and deal with at a large scale.</li><li>It helps to spread the information to individual Scrum teams via their representative. Hence, every team is informed about the current and to-be-achieved details of the project.&nbsp;</li><li>SoS meetings encourage a better decision-making process, which reduces the conflict among the team members regarding the project.&nbsp;</li><li>It makes the problem-solving process easier by discussing the issues and difficulties faced by any team.&nbsp;</li><li>Scrum of Scrums reinforces each team’s role, preventing them from drifting apart from project goals and putting them back on track.&nbsp;</li><li>It provides a way to handle new and unforeseen development problems that can affect multiple parts of the project and the team in the future.&nbsp;</li></ul><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/scrum_best_practices_836189da5b.png" alt="scrum best practices" srcset="https://cdn.marutitech.com/thumbnail_scrum_best_practices_836189da5b.png 245w,https://cdn.marutitech.com/small_scrum_best_practices_836189da5b.png 500w,https://cdn.marutitech.com/medium_scrum_best_practices_836189da5b.png 750w,https://cdn.marutitech.com/large_scrum_best_practices_836189da5b.png 1000w," sizes="100vw"></a></p>2b:T67c,<p>Scrum of Scrums is the best way to <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener">scale agile</a> to organizations with multiple teams. As for the Scrum of Scrums( SoS) meeting agenda, below are some of the SoS best practices to consider for getting the team composition right and conducting an effective meeting:&nbsp;</p><ul><li>Establish the length and frequency of every meeting ahead of time. Schedule to meet, not more than twice a week, with the time frame of regular scrum meetings, i.e., 15-30 minutes, tops.&nbsp;</li><li>Set aside time to address problems and prevent them from becoming a roadblock.&nbsp;</li><li>Track the progress of ongoing and finished scaled daily Scrum.</li><li>Encourage transparency between your team and establish a positive environment to create a collective agreement on the definition of “complete.”</li><li>Make sure each team is prepared to share its progress points in the meeting.</li><li>Deliver stories that depend on other teams early in the sprint so you can build in time to discover and address issues they might uncover.</li><li>Prepare and track a timeline for the team’s demo meeting.</li><li>Make sure the meeting attendees represent each team. Selecting the appropriate people will ensure a productive meeting.</li><li>Remember that Scrum meetings are not the same as status meetings. Status meetings are a holdover from waterfall methodology and have no place in agile practice.</li><li>Instruct each attendee to report back to their team about the meeting. If people don’t know why they are attending, what good are these meetings?</li></ul>2c:T5c3,<p><img src="https://cdn.marutitech.com/bc9a11b8-participants-of-scrum-of-scrums.png" alt="" srcset="https://cdn.marutitech.com/bc9a11b8-participants-of-scrum-of-scrums.png 1000w, https://cdn.marutitech.com/bc9a11b8-participants-of-scrum-of-scrums-768x704.png 768w, https://cdn.marutitech.com/bc9a11b8-participants-of-scrum-of-scrums-705x646.png 705w, https://cdn.marutitech.com/bc9a11b8-participants-of-scrum-of-scrums-450x413.png 450w" sizes="(max-width: 1000px) 100vw, 1000px" width="1000"></p><p>The Scrum of Scrums Meeting is not prescribed by an official description, as it depends on the theme of the sprint. For example, if the theme is user experience, one team can send an expert in this area. The teams can send different experts depending on the Sprint theme; however, one rule applies: nine people can be there in the end.</p><p>Although sending a Scrum master to a Scrum of Scrum meeting makes sense, shipping a product owner or development team member with more excellent technical knowledge might be even better. The Scrum of Scrum representative may change over time as issues arise.</p><p>The Scrum of Scrums can continue at higher levels as well. Meetings can occur not only among teams but also between experts, for instance, between two product owners, to discuss the feasibility of their product towards the market condition. It is not uncommon for this meeting to be called a “Scrum of Scrum of Scrums,” but this designation is not always used.</p>2d:T459,<p>The team itself should decide the frequency of this meeting. According to&nbsp;<a href="https://en.wikipedia.org/wiki/Ken_Schwaber" target="_blank" rel="noopener">Ken Schwaber</a>, the sessions should happen daily and last no longer than 15 minutes. However, a Scrum team may discover that it does not need to meet as often as initially planned.</p><p>It is more effective to schedule meetings less frequently yet for more extended periods. You can do it by holding two or three sessions a week instead of daily encounters. It allows team members to focus on any issues that may arise, rather than addressing them in the daily meeting, often revisiting prior problems and concerns.</p><p>When an issue is identified that requires attention and discussion, you must discuss it as soon as possible. When many people are involved in determining the issue, it is often a problem affecting the work of large groups of people. It deserves to be resolved as soon as possible. Therefore, while a scrum of scrums meeting may last only fifteen minutes, everyone should budget more time to discuss potential problems.</p>2e:Tda1,<p><img src="https://cdn.marutitech.com/79679e1a_agenda_of_scrum_of_scrums_01_min_be282173e6.png" alt="Agenda of Scrum of Scrums" srcset="https://cdn.marutitech.com/thumbnail_79679e1a_agenda_of_scrum_of_scrums_01_min_be282173e6.png 188w,https://cdn.marutitech.com/small_79679e1a_agenda_of_scrum_of_scrums_01_min_be282173e6.png 500w,https://cdn.marutitech.com/medium_79679e1a_agenda_of_scrum_of_scrums_01_min_be282173e6.png 750w,https://cdn.marutitech.com/large_79679e1a_agenda_of_scrum_of_scrums_01_min_be282173e6.png 1000w," sizes="100vw"></p><p>An excellent scrum of scrums agenda should reflect the format of the daily Scrum, which has the following questions to answer:</p><ul><li>What achievement has the team made since the last Scrum of Scrums meeting?</li><li>What will your team do before we meet again?</li><li>What limitations or hurdles are holding the team back?</li><li>Can an action taken by one team interfere with another team’s work?</li></ul><p>The Scrum of Scrums meeting begins by answering these four questions by each participant present in a relatively short and fast-paced manner. This helps the scrum ambassador ensure the operational effectiveness of each team and that they are working towards the common goal of the project.</p><p>During this part of the meeting, the facilitator should encourage participants to raise questions and issues but not discuss possible solutions until everyone has had a chance to answer the above questions.&nbsp;</p><p>One of the best techniques to achieve this is to leave the names out of the conversions, which can ultimately help you keep the discussion at the appropriate level of detail. This process aims to create a sense of coordination and cooperation between all the teams by involving cross-team synchronization across the organization.</p><p>Once the process is complete, the focus of the meeting shifts to address the issues and challenges discussed in the initial phase or maintained on the Scrum of Scrums backlog.</p><p><span style="font-size:16px;"><strong>SoS in Large Organizations</strong></span></p><p>A Scrum of Scrums framework can be very effective in large organizations with multiple teams, provided the Scrum of Scrum meetings are well-run and focus on solving issues that affect teams.&nbsp;</p><p>The purpose of a Scrum of Scrums meeting is not to report the progress of development teams to manage but rather make sure that the individual teams are fulfilling their sprint goals and that the overall project goal is accomplished.</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Did you find the video snippet on&nbsp;What are the benefits of smaller pizza-sized teams? to be insightful? We have a ~22 min video where Mitul Makadia gets into the weeds, and we discuss about Implementing &amp; Scaling Agile to streamline Software Development. Take a look –</i></span></p><div class="raw-html-embed"><iframe width="1500" height="844" src="https://www.youtube.com/embed/t9PeY145obc?feature=oembed&amp;wmode=opaque&amp;enablejsapi=1&amp;origin=https://marutitech.com" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen="" title="The Lab Cast Ep 03: Implementing and Scaling Agile to streamline Software Development | Podcast" data-lf-yt-playback-inspected-lynor8xobloawqjz="true" data-lf-yt-playback-inspected-jmvz8g0bmbe72pod="true" data-gtm-yt-inspected-8="true" id="964385917"></iframe></div>2f:T6a5,<p>Scrum of Scrums is a unique approach to lead your organization towards agility. It’s a method of holding meetings and tracking progress while maintaining productivity. SoS ensures that meetings are more efficient, streamlined, and effective. It can help your organization become more agile—as it allows for faster development cycles and improved communication amongst the various teams involved in any given project.</p><p>We hope you enjoyed learning about Scrum of Scrums and how you can implement it to help your team deliver products in a timely and cohesive manner.&nbsp;</p><p>Also read : <a href="https://marutitech.com/guide-to-scrum-sprint-planning/" target="_blank" rel="noopener">A Comprehensive Guide to Scrum Sprint Planning.</a></p><p>With over 12 years of experience in addressing business challenges with digital transformation, we have what it takes to help companies bridge the gap between digital vision and reality.</p><p>A perfect software product demands an equally excellent execution methodology. At <a href="https://marutitech.com/" target="_blank" rel="noopener">Maruti Techlabs,</a> follow Agile, Lean, and DevOps best practices to create a superior prototype that brings your users’ ideas to fruition through collaboration and rapid execution. Our Agile experts can also help you identify the possible impediments that can be destructive for your business in achieving sprint goals.</p><p>Get in touch with us for a free consultation and learn how our <a href="https://marutitech.com/services/software-product-engineering/" target="_blank" rel="noopener">product development services</a> can transform your business vision into market-ready software solutions.</p>30:T9ae,<p>Scrum is a popular Agile Framework for project management. It tends to be the most used <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener">Agile </a>manifesto globally. Scrum brings flexibility, transparency, and creativity to Project Management. Even though it was initially invented to be used in Software Development, it’s currently used in every possible field to offer inventive goods &amp; services to fulfill customers’ needs.</p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>Hey there! This blog is almost about 2600<strong>+</strong>&nbsp;words&nbsp;long &amp; not everyone likes to read that much. We understand that.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>This is precisely why we made a&nbsp;podcast&nbsp;on the topic. Mitul Makadia, CEO &amp; Founder of Maruti Techlabs, talks to Bikshita Bhattacharyya about his Agile implementation, strategy, and process during the nascent stages of the company.</i></span></p><p><span style="font-family:tahoma, arial, helvetica, sans-serif;"><i>He walks us through the challenges he faced, how the business benefited from scaling Agile, and how important is the scrum master in making Agile a success at the organizational level. Here is a small snippet from that episode. Check it out!</i></span></p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/yVFWzVP2m1s" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div><p>Sprint is at the core of Scrum. <a href="https://www.atlassian.com/agile/scrum/sprints" target="_blank" rel="noopener">A Sprint</a> is a finite period that is allotted to create a working product. At the end of the Sprint, a review is conducted to demonstrate the working product. In this comprehensive blog post, we will take you through the different stages of Sprint, Scrum events, Sprint planning, as well as how you can be prepared to take part in your first Scrum Sprint.</p><p>Using Scrum the right way requires a fundamental understanding of Agile manifesto, Scrum Framework, and associated processes. We can achieve this by defining a small work product, conducting a Proof Of Concept, and planning for more extensive product/application development based on the results and lessons learned during PoC.</p>31:Td05,<p><img src="https://cdn.marutitech.com/5_stages_of_scrum_sprint_9d9d275cdf.png" alt="5 stages of scrum sprint" srcset="https://cdn.marutitech.com/thumbnail_5_stages_of_scrum_sprint_9d9d275cdf.png 242w,https://cdn.marutitech.com/small_5_stages_of_scrum_sprint_9d9d275cdf.png 500w,https://cdn.marutitech.com/medium_5_stages_of_scrum_sprint_9d9d275cdf.png 750w,https://cdn.marutitech.com/large_5_stages_of_scrum_sprint_9d9d275cdf.png 1000w," sizes="100vw"></p><p>Sprints are the life of Scrum, where ideas are converted into value. Scrum processes tackle the specific activities and flow of a Scrum project. There are <a href="https://www.scrumstudy.com/whyscrum/scrum-phases-and-processes" target="_blank" rel="noopener">five stages</a> of the Scrum Sprint planning as follows :</p><p><strong>&nbsp; &nbsp; 1. Initiate/ Pre-planning </strong>– This phase includes the processes related to the commencement of a project.&nbsp; It involves deciding on and setting the scope and objectives for the project, creating and distributing its charter, and taking other steps to guarantee success. Some of the processes include creating project vision, identifying Scrum Master and stakeholder(s), forming Scrum team, developing epic(s), and creating a prioritized product backlog.<br>&nbsp;</p><p><strong>&nbsp; &nbsp; 2. Plan and Estimate</strong> -This phase involves planning and estimating processes, including creating user stories, approving, assessing, committing user stories, creating tasks, evaluating tasks, and creating a Sprint backlog.</p><p><strong>&nbsp; &nbsp; 3. Implement –</strong> This phase is about executing the tasks and activities to create a product. These activities include building the various outputs, conducting daily standup meetings, and <a href="https://marutitech.com/agile-product-backlog-grooming/" target="_blank" rel="noopener">grooming the product backlog</a>.&nbsp;</p><p><strong>&nbsp; &nbsp; 4. Review and Retrospect/ Test&nbsp; </strong>– This stage of the project lifecycle is concerned with evaluating what has been accomplished so far, whether the team has worked to plan, and how it can do things better in the future.</p><p><strong>&nbsp; &nbsp; 5. Release </strong>– This stage highlights delivering the accepted deliverables to the customer and determining, documenting, and absorbing the lessons learned during the project.</p><p>A project has various phases. These include Preliminary Phase, Planning Phase, Design Phase, Implementation Phase, Testing Phase, Deployment Phase, and Support Phase. You can find the complete list of the 19 Scrum processes, as described in SBOK® Guide <a href="https://www.scrumstudy.com/whyscrum/scrum-phases-and-processes" target="_blank" rel="noopener">here</a>.&nbsp;</p><p><a href="https://marutitech.com/case-study/revamping-real-estate-platform-using-agile-development/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/scrum_sprint_planning_e26fc4b14c.png" alt="scrum sprint planning" srcset="https://cdn.marutitech.com/thumbnail_scrum_sprint_planning_e26fc4b14c.png 245w,https://cdn.marutitech.com/small_scrum_sprint_planning_e26fc4b14c.png 500w,https://cdn.marutitech.com/medium_scrum_sprint_planning_e26fc4b14c.png 750w,https://cdn.marutitech.com/large_scrum_sprint_planning_e26fc4b14c.png 1000w," sizes="100vw"></a></p>32:Tdda,<p>Scrum teams deliver products iteratively and progressively, ensuring a potentially valuable version of a working product is always available. Each increment of the development cycle produces a potentially helpful package that can be feedbacked on, which can then enhance all future versions until the desired end state is reached.</p><p>Primarily, Scrum consists of&nbsp; <a href="https://www.ntaskmanager.com/blog/newbies-guide-to-scrum-project-management-101/" target="_blank" rel="noopener">4 formal events</a> or phases :</p><ul><li>Sprint Planning</li><li>Daily Scrum</li><li>Sprint Review</li><li>Sprint Retrospective</li></ul><p><img src="https://cdn.marutitech.com/4_scrum_events_3bcdcf404c.png" alt="4 scrum events" srcset="https://cdn.marutitech.com/thumbnail_4_scrum_events_3bcdcf404c.png 245w,https://cdn.marutitech.com/small_4_scrum_events_3bcdcf404c.png 500w,https://cdn.marutitech.com/medium_4_scrum_events_3bcdcf404c.png 750w,https://cdn.marutitech.com/large_4_scrum_events_3bcdcf404c.png 1000w," sizes="100vw"></p><p>The Sprint, which is the primary activity in Scrum, lasts between 1 and 4 weeks.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Sprint Planning Meeting</strong></span></h3><p>This meeting initiates the Sprint by rendering the activities and work contained. The development teams make Sprint backlogs for the Sprint. The Product Owner and the Development Team then determine the team’s tasks within the subsequent Sprint. Team members take up various tasks based on the highest priority and who they feel can best serve them with the most excellent effectiveness. The Scrum Team may also invite other people to attend Sprint Planning to provide guidance.</p><h3><strong>&nbsp;</strong><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong> &nbsp; 2. Daily Scrum or Daily Standup</strong></span></h3><p>It is a roughly 15-minute, daily event that highlights the progress towards the Sprint goal. Each team member shares the latest progress on their work and identifies any potential challenges. This daily meeting aims to ensure all the team members are on the same page and their activities in sync.</p><p>Daily Scrums improve communications, identify barriers or challenges, promote quick decision-making, and thus eliminate the need for other meetings.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Sprint Review</strong></span></h3><p>The Sprint Review is conducted at the end of each Sprint. Its objective is to examine the result of the Sprint and discuss the goals achieved. This review meeting also gives the stakeholders a chance to provide feedback and suggestions about the product.<br><br>The Sprint Review is the second last event of the Sprint. It is timeboxed to a limit of four hours for a one-month Sprint. For shorter Sprints, the event is generally faster.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 4. Sprint Retrospective</strong></span></h3><p>The Retrospective Meeting, also referred to as the RnR by Scrum teams, allows teams to assess their achievements at the end of a Sprint. It encourages open conversation about the successes and failures and identifies ways to strengthen activities during upcoming Sprints. The purpose of Sprint Retrospective is to plan ways to enhance both quality and efficiency.</p><p>The Sprint Retrospective ends the Sprint. It is timeboxed to the utmost of three hours for a one-month Sprint.</p>33:T9e4,<p><img src="https://cdn.marutitech.com/Scrum_Sprint_Planning_Why_What_and_How_05ffdd4de5.jpg" alt="Scrum Sprint Planning – Why, What &amp; How" srcset="https://cdn.marutitech.com/thumbnail_Scrum_Sprint_Planning_Why_What_and_How_05ffdd4de5.jpg 116w,https://cdn.marutitech.com/small_Scrum_Sprint_Planning_Why_What_and_How_05ffdd4de5.jpg 373w,https://cdn.marutitech.com/medium_Scrum_Sprint_Planning_Why_What_and_How_05ffdd4de5.jpg 559w,https://cdn.marutitech.com/large_Scrum_Sprint_Planning_Why_What_and_How_05ffdd4de5.jpg 746w," sizes="100vw"></p><p>The three questions about Sprint planning events:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Why?</strong></span></h3><p>Every Sprint is an investment. Both money and time are invested, which usually can’t be taken back. What’s spent is gone. Scrum demands that we have an idea of the price of these investments. We draft a Sprint objective to answer this question:</p><ul><li>Why do we invest in this product or service?&nbsp;</li><li>What result or impact are we looking to make with this investment?</li></ul><p>We seek to answer this why-question in the Sprint goal.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. What?</strong></span></h3><p>Now that we understand the purpose – the motive for running this Sprint – one must come up with the best idea of what to do to get there. It usually means we select backlog items that we think will realize the value we’re going for, help us achieve the Sprint goal. Hence, we come up with a prediction of what we want to do to achieve the result we’re investing in.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. How?</strong></span></h3><p>How do we get the work done? Where do we need to research, work together, design, re-use, or throw out?&nbsp; When there are multiple unknowns, planning to a high level of detail usually results in a lot of waste.</p><p><a href="https://marutitech.com/case-study/frontend-development-of-manufacturing-analytics-tool/" target="_blank" rel="noopener"><img src="https://cdn.marutitech.com/scrum_sprint_2803dfc753.png" alt="scrum sprint" srcset="https://cdn.marutitech.com/thumbnail_scrum_sprint_2803dfc753.png 245w,https://cdn.marutitech.com/small_scrum_sprint_2803dfc753.png 500w,https://cdn.marutitech.com/medium_scrum_sprint_2803dfc753.png 750w,https://cdn.marutitech.com/large_scrum_sprint_2803dfc753.png 1000w," sizes="100vw"></a></p>34:T950,<p>Before a Sprint commences, some planning is necessary. For your first Sprint to be a win, there are many measures you should take before you get started.&nbsp;</p><p><strong>Sprint Planning: </strong>This event is the Scrum Team’s first stride towards Sprint success. The Product Owner talks about the product backlog with the Development Team during this ceremony.</p><p>The Scrum Master assists the Scrum Team’s meeting, during which effort or story point estimates are done. The product backlog must include all the details for analysis (e.g., timeframes, specific steps, for what for which customer group, etc.) And the Product Owner must answer any questions that may arise regarding its content before the estimation.</p><p>Here are the things you must cover before your first Sprint:</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Internalize the Scrum values as a team</strong></span></h3><p>Imbibe the Scrum values to ensure your team can take control and organize themselves successfully.</p><p>&nbsp;If the team members can communicate well, there will be no need to take charge since everyone knows what they should do.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. Create a Project Roadmap</strong></span></h3><p>The product owner should work with the appropriate stakeholders to discuss high-level versions of end goals, short-term goals, and a flexible timeline to work around the project’s progress.</p><p>Note that a significant assessment of Agile methodology is preparation and flexibility. Your roadmap should be prepared as the project progresses, so it can be continuously adjusted as your business changes and grows, so it doesn’t need to be complete or flawless right away.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. Collaborate with Stakeholders on Product Backlog</strong></span></h3><p>As a project manager, you need to work with your team and the shareholders to: add, review, and prioritize product backlog items.</p><p><strong>Outcome: </strong>The Development Team’s work can be decided during the Sprint — the Sprint goal. It’s an expansion of complete work, and everyone should feel confident about the dedication. There might be a lot of negotiation that occurs during this ceremony.</p>35:T512,<ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Establishes a Communication Platform for the Scrum Team</strong></span></li></ul><p>When the Sprint Planning event occurs, the team members can recognize their ability and dependencies to achieve the goals effectively. So, they can then plan their work to achieve those goals during their ongoing Sprint effectively.</p><ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Helps in Prioritizing the Deliverable</strong></span></li></ul><p>The product owner is responsible for choosing which items from the backlog are implemented in a Sprint. The product owner prioritizes the importance of each item and may also cut things down, in length or entirely if needed, making them more “doable” for a given Sprint. This way, only the essential features of the product get completed during early development.</p><ul><li><span style="font-family:Raleway, sans-serif;font-size:16px;"><strong>Prevents Scrum Team Burnout</strong></span></li></ul><p>The team will set its targets clearly since developers will select the goals according to their estimations and capabilities. This way, there won’t need to be any involvement of a third party that could set unachievable goals for the Scrum Team.</p>36:T100a,<p><img src="https://cdn.marutitech.com/scrum_artifacts_explained_3e796b1976.png" alt="scrum_artifacts_explained" srcset="https://cdn.marutitech.com/thumbnail_scrum_artifacts_explained_3e796b1976.png 245w,https://cdn.marutitech.com/small_scrum_artifacts_explained_3e796b1976.png 500w,https://cdn.marutitech.com/medium_scrum_artifacts_explained_3e796b1976.png 750w,https://cdn.marutitech.com/large_scrum_artifacts_explained_3e796b1976.png 1000w," sizes="100vw"></p><p>Scrum’s artifacts represent work or value. They are information that a scrum team and stakeholders use to outline the product being developed, actions required to produce it, and the actions performed during the project. Scrum artifacts are designed to maximize the transparency of key information.&nbsp;</p><p><a href="https://resources.scrumalliance.org/Article/scrum-artifacts" target="_blank" rel="noopener">Scrum Artifacts</a> such as the Sprint backlog and product backlog contain a commitment that defines how they will provide information. For example, the product backlog has the project’s goal.</p><p>These commitments exist to strengthen the Scrum values for the Scrum Team and its stakeholders.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 1. Product Backlog</strong></span></h3><p>The product backlog lists prioritized features, enhancements, bug fixes, tasks, or work requirements needed to build the end product. The primary source of requirements is compiled from input sources like customer support, competitor analysis, market demands, and general business analysis. The Product Backlog is a highly visible and “live” artifact at the heart of the Scrum framework accessible for all the projects. It is updated on-demand as new data is available.&nbsp;</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 2. The Sprint Backlog</strong></span></h3><p>The Sprint Backlog covers a list of tasks that the Scrum team has to achieve by the end of the Sprint. The development teams make Sprint backlogs to plan outputs and solutions for upcoming increments and detail the work needed to create the increment.&nbsp; It is a planned process containing complete information that helps to clearly understand the changes carried out in the development during the Daily Scrum.</p><p>Sprint backlogs are created by picking a task from the product backlog and splitting that task into smaller, actionable Sprint items. If a team does not have the bandwidth to deliver all the Sprint tasks, the remaining tasks will stand by in the Sprint backlog for a later Sprint.</p><h3><span style="font-family:Poppins, sans-serif;font-size:18px;"><strong>&nbsp; &nbsp; 3. The Product Increment</strong></span></h3><p>The word “Increment” itself describes the increase to the next stage. The increment is a step in the direction of a goal or vision. The Product Increment comprises a list of Product Backlog items completed during the Sprint and the former Sprints. By the end of Sprint, the Scrum team should conclude every backlog item.&nbsp;</p><p>An Increment is the customer deliverables that were produced by completing product backlog tasks during a Sprint. In a nutshell, there is always one for every Sprint in a single increment. And an increment is determined during the scrum planning phase. An increment happens if the team chooses to release it to the customer. If needed, product increments can complement CI/CD tracking and version rollback.</p><p><i>Did you find the video snippet on How does a scrum master ensure that everyone is on the same page?&nbsp;to be insightful? We have a ~22 min video where Mitul Makadia gets into the weeds, and we discuss about Implementing &amp; Scaling Agile to streamline Software Development. Take a look –</i></p><div class="raw-html-embed"><iframe width="560" height="315" src="https://www.youtube.com/embed/t9PeY145obc" title="YouTube video player" frameborder="0" allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" allowfullscreen=""></iframe></div>37:T9d3,<p>If you’re new to Scrum, you might be wondering what happens during a Scrum Sprint. In this blog, we have covered the essential topics related to Scrum Sprint so you can see how the process works. It can be a significant change from how you might have done work before, so it’s helpful to understand the Scrum Sprint stages, various scrum events, Sprint planning, and checklist, as well as the pros and cons of Sprint planning.&nbsp;</p><p>The methodology of agile development has proven to be a winning formula for product development projects. It has allowed companies to successfully deliver their software products on time, meeting all objectives without sacrificing quality.&nbsp;</p><p>If you want to do a Scrum sprint but don't have enough resources, you can find an <a href="https://marutitech.com/services/staff-augmentation/hire-agile-developers/" target="_blank" rel="noopener"><span style="color:#f05443;">Agile offshore development team</span></a>. Having experts in managing Agile demands and capacity on your team will help with Sprint planning.</p><p>By staying flexible, adaptable, and nimble, <a href="https://marutitech.com/" target="_blank" rel="noopener">Maruti Techlabs</a> has been able to help companies across all industries achieve their product development goals through Agile methodology and Scrum Sprints.<br><br>As a product development partner that has worked remotely with more than 90% of its clientele, it is imperative for us to define Scrum guidelines and processes with our clients beforehand for a successful partnership. The Scrum methodology and its various stages are the first steps we take before deploying teams. At <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener">Maruti Techlabs</a>, we believe it is imperative to lay a solid groundwork for an effective partnership between remote development teams at our side and the client-side. It is where we make utmost use of Scrum guidelines and <a href="https://marutitech.com/guide-to-scaled-agile-frameworks/" target="_blank" rel="noopener">Agile frameworks</a>.<br><br>If you’d like to learn more about how this could benefit you, connect <a href="https://marutitech.com/contact-us/" target="_blank" rel="noopener">with our team</a> for a free consultation and see how we can help you consistently deliver and hit Sprint goals with our exceptional <a href="https://marutitech.com/services/software-product-engineering/" target="_blank" rel="noopener">product development services</a>.</p>2:[null,["$","$L12",null,{"blogData":{"data":[{"id":228,"attributes":{"createdAt":"2022-09-15T07:30:52.073Z","updatedAt":"2025-06-16T10:42:14.995Z","publishedAt":"2022-09-15T11:01:27.697Z","title":"How To Reinvent the Scrum Process for Modern Distributed Teams","description":"How are distributed agile teams and Scrum compatible? Let's understand it by the distributed scrum team. ","type":"Agile","slug":"distributed-scrum-team","content":[{"id":13974,"title":null,"description":"$13","twitter_link":null,"twitter_link_text":null},{"id":13975,"title":"What is a Distributed Scrum Team?","description":"$14","twitter_link":null,"twitter_link_text":null},{"id":13976,"title":"What are the Challenges for Distributed Teams?","description":"$15","twitter_link":null,"twitter_link_text":null},{"id":13977,"title":"Tips to Manage and Build an Effective Distributed Team","description":"$16","twitter_link":null,"twitter_link_text":null},{"id":13978,"title":"Distributed Team Models","description":"$17","twitter_link":null,"twitter_link_text":null},{"id":13979,"title":"Totally Integrated Scrum","description":"$18","twitter_link":null,"twitter_link_text":null},{"id":13980,"title":"\nHow Can We Reinvent the Scrum Retrospective Process for the Modern Distributed Team? \n","description":"$19","twitter_link":null,"twitter_link_text":null},{"id":13981,"title":"Conclusion","description":"$1a","twitter_link":null,"twitter_link_text":null}],"heroSection_image":{"data":{"id":461,"attributes":{"name":"colleagues-brainstorming-together (1).jpg","alternativeText":"colleagues-brainstorming-together (1).jpg","caption":"colleagues-brainstorming-together (1).jpg","width":6513,"height":3664,"formats":{"thumbnail":{"name":"thumbnail_colleagues-brainstorming-together (1).jpg","hash":"thumbnail_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":7.69,"sizeInBytes":7689,"url":"https://cdn.marutitech.com//thumbnail_colleagues_brainstorming_together_1_a35fab683f.jpg"},"small":{"name":"small_colleagues-brainstorming-together (1).jpg","hash":"small_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":281,"size":22.66,"sizeInBytes":22656,"url":"https://cdn.marutitech.com//small_colleagues_brainstorming_together_1_a35fab683f.jpg"},"medium":{"name":"medium_colleagues-brainstorming-together (1).jpg","hash":"medium_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":42.64,"sizeInBytes":42638,"url":"https://cdn.marutitech.com//medium_colleagues_brainstorming_together_1_a35fab683f.jpg"},"large":{"name":"large_colleagues-brainstorming-together (1).jpg","hash":"large_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":563,"size":66.67,"sizeInBytes":66672,"url":"https://cdn.marutitech.com//large_colleagues_brainstorming_together_1_a35fab683f.jpg"}},"hash":"colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","size":962.34,"url":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:49:44.542Z","updatedAt":"2024-12-16T11:49:44.542Z"}}},"audio_file":{"data":null},"suggestions":{"id":1994,"blogs":{"data":[{"id":148,"attributes":{"createdAt":"2022-09-13T11:53:23.984Z","updatedAt":"2025-06-16T10:42:04.849Z","publishedAt":"2022-09-13T12:25:14.075Z","title":"Understanding Scrum Board: Structure, Working, Benefits & More","description":"Learn everything about the scrum board, its functionality, how they work & why you should choose them.","type":"Agile","slug":"understanding-scrum-board","content":[{"id":13436,"title":null,"description":"$1b","twitter_link":null,"twitter_link_text":null},{"id":13437,"title":"Scrum: History & Origin","description":"<p>Although Scrum is the most common terminology while dealing with Agile development, many people are unaware that “Scrum” was coined before “Agile Development.”&nbsp;</p><p>The term “Scrum” was introduced in 1986 by <a href=\"https://www.scruminc.com/takeuchi-and-nonaka-roots-of-scrum/\" target=\"_blank\" rel=\"noopener\">Nonaka and Takeuchi</a>. They derived the word “Scrum” from the traditional England football game rugby, which indicates the importance of teamwork while handling complex problems. The study published in Harvard Business Review explained the evidence of small cross-functional teams producing the maximum outputs.&nbsp;&nbsp;</p><p>In 1993, Jeff Sutherland initiated Scrum for Software development for the first time at Easel Corporation. Later in 2001, the Agile Manifesto defined the principle of software development derived from the wide range of Agile frameworks such as Scrum and Kanban.</p>","twitter_link":null,"twitter_link_text":null},{"id":13438,"title":"What is a Scrum Board?","description":"$1c","twitter_link":null,"twitter_link_text":null},{"id":13439,"title":"Structure of a Scrum Board","description":"$1d","twitter_link":null,"twitter_link_text":null},{"id":13440,"title":"Types of Scrum Board","description":"$1e","twitter_link":null,"twitter_link_text":null},{"id":13441,"title":"What is the Difference Between a Scrum and Kanban Board?","description":"$1f","twitter_link":null,"twitter_link_text":null},{"id":13442,"title":"Working of Scrum Board ","description":"$20","twitter_link":null,"twitter_link_text":null},{"id":13443,"title":"\nBenefits of a Scrum board \n","description":"$21","twitter_link":null,"twitter_link_text":null},{"id":13444,"title":"5 Handy Tips on Creating an Effective Scrum Board ","description":"$22","twitter_link":null,"twitter_link_text":null},{"id":13445,"title":"Using the Right Tools for the Job","description":"$23","twitter_link":null,"twitter_link_text":null},{"id":13446,"title":"Conclusion ","description":"$24","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":465,"attributes":{"name":"adult-woman-planning-project-office (1).jpg","alternativeText":"adult-woman-planning-project-office (1).jpg","caption":"adult-woman-planning-project-office (1).jpg","width":6973,"height":3922,"formats":{"medium":{"name":"medium_adult-woman-planning-project-office (1).jpg","hash":"medium_adult_woman_planning_project_office_1_6bcbe6c3a0","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":29.61,"sizeInBytes":29607,"url":"https://cdn.marutitech.com//medium_adult_woman_planning_project_office_1_6bcbe6c3a0.jpg"},"thumbnail":{"name":"thumbnail_adult-woman-planning-project-office (1).jpg","hash":"thumbnail_adult_woman_planning_project_office_1_6bcbe6c3a0","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":5.69,"sizeInBytes":5690,"url":"https://cdn.marutitech.com//thumbnail_adult_woman_planning_project_office_1_6bcbe6c3a0.jpg"},"small":{"name":"small_adult-woman-planning-project-office (1).jpg","hash":"small_adult_woman_planning_project_office_1_6bcbe6c3a0","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":281,"size":16.4,"sizeInBytes":16404,"url":"https://cdn.marutitech.com//small_adult_woman_planning_project_office_1_6bcbe6c3a0.jpg"},"large":{"name":"large_adult-woman-planning-project-office (1).jpg","hash":"large_adult_woman_planning_project_office_1_6bcbe6c3a0","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":562,"size":46.86,"sizeInBytes":46862,"url":"https://cdn.marutitech.com//large_adult_woman_planning_project_office_1_6bcbe6c3a0.jpg"}},"hash":"adult_woman_planning_project_office_1_6bcbe6c3a0","ext":".jpg","mime":"image/jpeg","size":775.22,"url":"https://cdn.marutitech.com//adult_woman_planning_project_office_1_6bcbe6c3a0.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:50:04.407Z","updatedAt":"2024-12-16T11:50:04.407Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}},{"id":216,"attributes":{"createdAt":"2022-09-15T07:30:48.500Z","updatedAt":"2025-06-16T10:42:13.276Z","publishedAt":"2022-09-15T10:54:24.522Z","title":"Guide to Scrum of Scrums: An Answer to Large-Scale Agile","description":"Check how Scrum of Scrums can help your organization become more agile. ","type":"Agile","slug":"guide-to-scrum-of-scrums","content":[{"id":13870,"title":null,"description":"$25","twitter_link":null,"twitter_link_text":null},{"id":13871,"title":"History of Scrum of Scrums(SoS)","description":"$26","twitter_link":null,"twitter_link_text":null},{"id":13872,"title":"What is Scrum of Scrums?","description":"$27","twitter_link":null,"twitter_link_text":null},{"id":13873,"title":"How does SOS work?","description":"<p>Scrum of Scrums divides a large team into smaller scrum teams or subteams. Each subteam will have its daily standups, sprint planning sessions, and other events as part of a Scrum of Scrums meetings.&nbsp;</p><p>The basic idea is to give each subteam the autonomy to plan their work independently while still coordinating with the rest of the team—just as independent teams do in a traditional scrum. Here, the large number of people divided into smaller scrum teams can include up to 10 members in each team.&nbsp;</p><p>Each team chooses one developer to act as spokesperson, often known as “ambassador” for daily standups during their scaled Scrum. Another role is the Scrum of Scrums master, similar to the Scrum Master for Scrum methodology but at a higher level.&nbsp;</p>","twitter_link":null,"twitter_link_text":null},{"id":13874,"title":"Purpose of Scrum of Scrums","description":"$28","twitter_link":null,"twitter_link_text":null},{"id":13875,"title":"\nStructure of the Scrum of Scrums\n","description":"$29","twitter_link":null,"twitter_link_text":null},{"id":13876,"title":"\nBenefits of a Scrum of Scrums \n","description":"$2a","twitter_link":null,"twitter_link_text":null},{"id":13877,"title":"Scrum of Scrums Best Practices ","description":"$2b","twitter_link":null,"twitter_link_text":null},{"id":13878,"title":"\nWho Attends Scrum of Scrums?\n","description":"$2c","twitter_link":null,"twitter_link_text":null},{"id":13879,"title":"Frequency of Meeting ","description":"$2d","twitter_link":null,"twitter_link_text":null},{"id":13880,"title":"Agenda of Scrum of Scrums","description":"$2e","twitter_link":null,"twitter_link_text":null},{"id":13881,"title":"Conclusion","description":"$2f","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":425,"attributes":{"name":"3562ec98-scrumofscrums-min.jpg","alternativeText":"3562ec98-scrumofscrums-min.jpg","caption":"3562ec98-scrumofscrums-min.jpg","width":1000,"height":667,"formats":{"thumbnail":{"name":"thumbnail_3562ec98-scrumofscrums-min.jpg","hash":"thumbnail_3562ec98_scrumofscrums_min_290bc1bb55","ext":".jpg","mime":"image/jpeg","path":null,"width":234,"height":156,"size":8.62,"sizeInBytes":8622,"url":"https://cdn.marutitech.com//thumbnail_3562ec98_scrumofscrums_min_290bc1bb55.jpg"},"small":{"name":"small_3562ec98-scrumofscrums-min.jpg","hash":"small_3562ec98_scrumofscrums_min_290bc1bb55","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":334,"size":32.23,"sizeInBytes":32229,"url":"https://cdn.marutitech.com//small_3562ec98_scrumofscrums_min_290bc1bb55.jpg"},"medium":{"name":"medium_3562ec98-scrumofscrums-min.jpg","hash":"medium_3562ec98_scrumofscrums_min_290bc1bb55","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":500,"size":65.95,"sizeInBytes":65947,"url":"https://cdn.marutitech.com//medium_3562ec98_scrumofscrums_min_290bc1bb55.jpg"}},"hash":"3562ec98_scrumofscrums_min_290bc1bb55","ext":".jpg","mime":"image/jpeg","size":105.65,"url":"https://cdn.marutitech.com//3562ec98_scrumofscrums_min_290bc1bb55.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:47:08.173Z","updatedAt":"2024-12-16T11:47:08.173Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}},{"id":221,"attributes":{"createdAt":"2022-09-15T07:30:50.081Z","updatedAt":"2025-06-16T10:42:13.961Z","publishedAt":"2022-09-15T10:58:22.826Z","title":"Planning Your Scrum Sprint: A Step-by-Step Guide to Agile Success","description":"Explore the essential topics related to scrum sprinting and learn about how the process works.","type":"Agile","slug":"guide-to-scrum-sprint-planning","content":[{"id":13909,"title":null,"description":"$30","twitter_link":null,"twitter_link_text":null},{"id":13910,"title":"What is Sprint Planning?","description":"<p>In Scrum, every project is broken down into time blocks called Sprints. Sprints can vary in length but are usually 2-4 weeks long. A Sprint planning meeting is a periodic meeting that involves the entire team, including the Scrum Master, Scrum Product Manager, and Scrum Team. They meet to decide the scope of the current Sprint and which backlog items will be taken care of in the next Sprint. The Sprint planning Scrum event is a collective process that allows team members to say when work happens.</p><p>A successful Sprint planning session will give two critical strategic items:</p><ol><li><strong>The Sprint goal:</strong> This includes a brief written summary of the team’s plans to achieve in the next Sprint.</li><li><strong>The Sprint backlog: </strong>The team has concurred to work on the list of stories and other product backlog items in the forthcoming Sprint.</li></ol>","twitter_link":null,"twitter_link_text":null},{"id":13911,"title":"5 Stages of Scrum Sprint","description":"$31","twitter_link":null,"twitter_link_text":null},{"id":13912,"title":"Which are the 4 Scrum Events?","description":"$32","twitter_link":null,"twitter_link_text":null},{"id":13913,"title":"Scrum Sprint Planning – Why, What & How","description":"$33","twitter_link":null,"twitter_link_text":null},{"id":13914,"title":"Scrum Sprint Planning: Things To Do Before Your First Sprint","description":"$34","twitter_link":null,"twitter_link_text":null},{"id":13915,"title":"Scrum Sprint Planning Checklist","description":"<p><img src=\"https://cdn.marutitech.com/Scrum_Sprint_Planning_Checklist_63ee519852.jpg\" alt=\"Scrum Sprint Planning Checklist\" srcset=\"https://cdn.marutitech.com/thumbnail_Scrum_Sprint_Planning_Checklist_63ee519852.jpg 130w,https://cdn.marutitech.com/small_Scrum_Sprint_Planning_Checklist_63ee519852.jpg 416w,https://cdn.marutitech.com/medium_Scrum_Sprint_Planning_Checklist_63ee519852.jpg 623w,https://cdn.marutitech.com/large_Scrum_Sprint_Planning_Checklist_63ee519852.jpg 831w,\" sizes=\"100vw\"></p><p>To be equipped during your Sprint planning meetings, here is a checklist you should keep handy :</p><ul><li>Come ready with data and evaluated story points.</li><li>Verify estimated story points for all items on the backlog</li><li>Decide on the items to move to the new Sprint.</li><li>Determine the team’s bandwidth for the next Sprint and compare it with the total story points suggested</li><li>Conclude the meeting with Q&amp;A session to make sure all team members are on the same page</li></ul>","twitter_link":null,"twitter_link_text":null},{"id":13916,"title":"Advantages of Sprint Planning","description":"$35","twitter_link":null,"twitter_link_text":null},{"id":13917,"title":"Disadvantages of Sprint Planning","description":"<ul><li><strong>Lackluster Calculations can Lead to Failures</strong></li></ul><p>As tasks during the current Sprint will be counted based on estimates from developers, the ability to reach a Sprint goal can be hindered by unreliable and wrong estimations.</p><ul><li><strong>Appropriate Knowledge of Scrum is Mandatory to Carry Out Sprint Planning</strong></li></ul><p>For a successful Sprint Planning session, the team should be highly informed and aware of the various <a href=\"https://marutitech.com/guide-to-scaled-agile-frameworks/#Conclusion_Should_You_Use_the_Scaled_Agile_Framework\" target=\"_blank\" rel=\"noopener\">Scrum frameworks</a>. Lack of proper knowledge can cause Sprint Planning to be unsuccessful.</p>","twitter_link":null,"twitter_link_text":null},{"id":13918,"title":"Scrum Artifacts Explained","description":"$36","twitter_link":null,"twitter_link_text":null},{"id":13919,"title":"\nConclusion\n","description":"$37","twitter_link":null,"twitter_link_text":null}],"image":{"data":{"id":460,"attributes":{"name":"close-up-team-preparing-business-plan (1).jpg","alternativeText":"close-up-team-preparing-business-plan (1).jpg","caption":"close-up-team-preparing-business-plan (1).jpg","width":6015,"height":3384,"formats":{"thumbnail":{"name":"thumbnail_close-up-team-preparing-business-plan (1).jpg","hash":"thumbnail_close_up_team_preparing_business_plan_1_990b0d1bf0","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":5.61,"sizeInBytes":5610,"url":"https://cdn.marutitech.com//thumbnail_close_up_team_preparing_business_plan_1_990b0d1bf0.jpg"},"small":{"name":"small_close-up-team-preparing-business-plan (1).jpg","hash":"small_close_up_team_preparing_business_plan_1_990b0d1bf0","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":282,"size":13.97,"sizeInBytes":13974,"url":"https://cdn.marutitech.com//small_close_up_team_preparing_business_plan_1_990b0d1bf0.jpg"},"medium":{"name":"medium_close-up-team-preparing-business-plan (1).jpg","hash":"medium_close_up_team_preparing_business_plan_1_990b0d1bf0","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":24.33,"sizeInBytes":24329,"url":"https://cdn.marutitech.com//medium_close_up_team_preparing_business_plan_1_990b0d1bf0.jpg"},"large":{"name":"large_close-up-team-preparing-business-plan (1).jpg","hash":"large_close_up_team_preparing_business_plan_1_990b0d1bf0","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":563,"size":36.33,"sizeInBytes":36329,"url":"https://cdn.marutitech.com//large_close_up_team_preparing_business_plan_1_990b0d1bf0.jpg"}},"hash":"close_up_team_preparing_business_plan_1_990b0d1bf0","ext":".jpg","mime":"image/jpeg","size":476.51,"url":"https://cdn.marutitech.com//close_up_team_preparing_business_plan_1_990b0d1bf0.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:49:38.292Z","updatedAt":"2024-12-16T11:49:38.292Z"}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]}}}]}},"caseStudy_suggestions":{"id":1994,"title":"Overhauling a High-Performance Property Listing Platform","link":"https://marutitech.com/case-study/revamping-real-estate-platform-using-agile-development/","cover_image":{"data":{"id":430,"attributes":{"name":"14 (1).png","alternativeText":"14 (1).png","caption":"14 (1).png","width":1440,"height":358,"formats":{"thumbnail":{"name":"thumbnail_14 (1).png","hash":"thumbnail_14_1_80af7a587f","ext":".png","mime":"image/png","path":null,"width":245,"height":61,"size":20.82,"sizeInBytes":20822,"url":"https://cdn.marutitech.com//thumbnail_14_1_80af7a587f.png"},"small":{"name":"small_14 (1).png","hash":"small_14_1_80af7a587f","ext":".png","mime":"image/png","path":null,"width":500,"height":124,"size":78.81,"sizeInBytes":78809,"url":"https://cdn.marutitech.com//small_14_1_80af7a587f.png"},"medium":{"name":"medium_14 (1).png","hash":"medium_14_1_80af7a587f","ext":".png","mime":"image/png","path":null,"width":750,"height":186,"size":175.93,"sizeInBytes":175925,"url":"https://cdn.marutitech.com//medium_14_1_80af7a587f.png"},"large":{"name":"large_14 (1).png","hash":"large_14_1_80af7a587f","ext":".png","mime":"image/png","path":null,"width":1000,"height":249,"size":307.99,"sizeInBytes":307990,"url":"https://cdn.marutitech.com//large_14_1_80af7a587f.png"}},"hash":"14_1_80af7a587f","ext":".png","mime":"image/png","size":104.26,"url":"https://cdn.marutitech.com//14_1_80af7a587f.png","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:47:24.831Z","updatedAt":"2024-12-16T11:47:24.831Z"}}}},"authors":{"data":[{"id":12,"attributes":{"createdAt":"2022-09-02T07:16:28.390Z","updatedAt":"2025-06-16T10:42:34.307Z","publishedAt":"2022-09-02T07:16:30.042Z","name":"Mitul Makadia","designation":null,"description":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(119,119,119);font-family:Roboto, RobotoDraft, Helvetica, Arial, sans-serif;\">Mitul is the Founder and CEO of Maruti Techlabs. From developing business strategies for our clients to building teams and ensuring teamwork at every level, he runs the show quite effortlessly.</span></p>","slug":"mitul-makadia","linkedin_link":"https://www.linkedin.com/in/mitulmakadia/","twitter_link":"https://twitter.com/mitulmakadia","image":{"data":[{"id":526,"attributes":{"name":"Mitul Makadia.jpg","alternativeText":"Mitul Makadia.jpg","caption":"Mitul Makadia.jpg","width":2160,"height":2160,"formats":{"thumbnail":{"name":"thumbnail_Mitul Makadia.jpg","hash":"thumbnail_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":156,"height":156,"size":4.15,"sizeInBytes":4154,"url":"https://cdn.marutitech.com//thumbnail_Mitul_Makadia_20fa5e0703.jpg"},"small":{"name":"small_Mitul Makadia.jpg","hash":"small_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":500,"size":23.47,"sizeInBytes":23472,"url":"https://cdn.marutitech.com//small_Mitul_Makadia_20fa5e0703.jpg"},"medium":{"name":"medium_Mitul Makadia.jpg","hash":"medium_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":750,"size":47.3,"sizeInBytes":47298,"url":"https://cdn.marutitech.com//medium_Mitul_Makadia_20fa5e0703.jpg"},"large":{"name":"large_Mitul Makadia.jpg","hash":"large_Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":1000,"size":80.77,"sizeInBytes":80773,"url":"https://cdn.marutitech.com//large_Mitul_Makadia_20fa5e0703.jpg"}},"hash":"Mitul_Makadia_20fa5e0703","ext":".jpg","mime":"image/jpeg","size":292.41,"url":"https://cdn.marutitech.com//Mitul_Makadia_20fa5e0703.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:55:03.157Z","updatedAt":"2024-12-16T11:55:03.157Z"}}]}}}]},"seo":{"id":2224,"title":"How To Reinvent the Scrum Process for Modern Distributed Teams","description":"A distributed scrum team, also known as a remote team, is a group of people working as a team on the same project but located at different locations.","type":"article","url":"https://marutitech.com/distributed-scrum-team/","site_name":"Maruti Techlabs","locale":"en-US","schema":null,"image":{"data":{"id":461,"attributes":{"name":"colleagues-brainstorming-together (1).jpg","alternativeText":"colleagues-brainstorming-together (1).jpg","caption":"colleagues-brainstorming-together (1).jpg","width":6513,"height":3664,"formats":{"thumbnail":{"name":"thumbnail_colleagues-brainstorming-together (1).jpg","hash":"thumbnail_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":7.69,"sizeInBytes":7689,"url":"https://cdn.marutitech.com//thumbnail_colleagues_brainstorming_together_1_a35fab683f.jpg"},"small":{"name":"small_colleagues-brainstorming-together (1).jpg","hash":"small_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":281,"size":22.66,"sizeInBytes":22656,"url":"https://cdn.marutitech.com//small_colleagues_brainstorming_together_1_a35fab683f.jpg"},"medium":{"name":"medium_colleagues-brainstorming-together (1).jpg","hash":"medium_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":42.64,"sizeInBytes":42638,"url":"https://cdn.marutitech.com//medium_colleagues_brainstorming_together_1_a35fab683f.jpg"},"large":{"name":"large_colleagues-brainstorming-together (1).jpg","hash":"large_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":563,"size":66.67,"sizeInBytes":66672,"url":"https://cdn.marutitech.com//large_colleagues_brainstorming_together_1_a35fab683f.jpg"}},"hash":"colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","size":962.34,"url":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:49:44.542Z","updatedAt":"2024-12-16T11:49:44.542Z"}}}},"image":{"data":{"id":461,"attributes":{"name":"colleagues-brainstorming-together (1).jpg","alternativeText":"colleagues-brainstorming-together (1).jpg","caption":"colleagues-brainstorming-together (1).jpg","width":6513,"height":3664,"formats":{"thumbnail":{"name":"thumbnail_colleagues-brainstorming-together (1).jpg","hash":"thumbnail_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":245,"height":138,"size":7.69,"sizeInBytes":7689,"url":"https://cdn.marutitech.com//thumbnail_colleagues_brainstorming_together_1_a35fab683f.jpg"},"small":{"name":"small_colleagues-brainstorming-together (1).jpg","hash":"small_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":500,"height":281,"size":22.66,"sizeInBytes":22656,"url":"https://cdn.marutitech.com//small_colleagues_brainstorming_together_1_a35fab683f.jpg"},"medium":{"name":"medium_colleagues-brainstorming-together (1).jpg","hash":"medium_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":750,"height":422,"size":42.64,"sizeInBytes":42638,"url":"https://cdn.marutitech.com//medium_colleagues_brainstorming_together_1_a35fab683f.jpg"},"large":{"name":"large_colleagues-brainstorming-together (1).jpg","hash":"large_colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","path":null,"width":1000,"height":563,"size":66.67,"sizeInBytes":66672,"url":"https://cdn.marutitech.com//large_colleagues_brainstorming_together_1_a35fab683f.jpg"}},"hash":"colleagues_brainstorming_together_1_a35fab683f","ext":".jpg","mime":"image/jpeg","size":962.34,"url":"https://cdn.marutitech.com//colleagues_brainstorming_together_1_a35fab683f.jpg","previewUrl":null,"provider":"@strapi-community/strapi-provider-upload-google-cloud-storage","provider_metadata":null,"createdAt":"2024-12-16T11:49:44.542Z","updatedAt":"2024-12-16T11:49:44.542Z"}}},"blog_related_service":null}}],"meta":{"pagination":{"page":1,"pageSize":1000,"pageCount":1,"total":1}}}}]]
